# CentOS 8 原生部署总结

## 概述

本文档提供了在CentOS 8服务器上原生部署社交媒体管理系统的完整方案，不使用Docker容器化。

## 系统架构

```
Internet
    ↓
Nginx (反向代理) :80
    ↓
┌─────────────────┬─────────────────┐
│   Frontend      │    Backend      │
│   (静态文件)     │   (FastAPI)     │
│   Nginx服务     │   :8000         │
└─────────────────┴─────────────────┘
            ↓
    SQLite Database
    (/home/<USER>/social-media-management/data/)
```

## 环境要求

- **操作系统**: CentOS 8.x
- **Python**: 3.11.13 (需要编译安装)
- **Node.js**: 24.x
- **内存**: 最低4GB，推荐8GB+
- **存储**: 最低20GB可用空间
- **网络**: 能够访问外网下载依赖

## 部署文件说明

### 1. 核心脚本
- `centos8-one-click-deploy.sh` - 一键部署脚本（推荐）
- `native-deploy.sh` - 通用原生部署脚本
- `upload-to-centos8.sh` - 项目上传脚本

### 2. 配置文件
- `configs/supervisor-backend.ini` - Supervisor服务配置
- `configs/nginx-social-media.conf` - Nginx反向代理配置
- `centos8-deploy-guide.md` - 详细部署指南

### 3. 文档
- `CENTOS8_DEPLOYMENT_SUMMARY.md` - 本文档
- `centos8-deploy-guide.md` - 详细部署指南

## 快速部署步骤

### 方式一：一键部署（推荐）

```bash
# 1. 本地上传项目
./upload-to-centos8.sh -h YOUR_SERVER_IP

# 2. 登录服务器
ssh root@YOUR_SERVER_IP

# 3. 进入项目目录
cd /home/<USER>/social-media-management

# 4. 运行一键部署
./centos8-one-click-deploy.sh
```

### 方式二：手动部署

参考 `centos8-deploy-guide.md` 文档进行手动部署。

## 服务管理

### 启动/停止服务

```bash
# 后端服务
supervisorctl start social-media-backend
supervisorctl stop social-media-backend
supervisorctl restart social-media-backend

# Nginx服务
systemctl start nginx
systemctl stop nginx
systemctl restart nginx

# 查看服务状态
supervisorctl status
systemctl status nginx
```

### 日志查看

```bash
# 后端日志
tail -f /home/<USER>/social-media-management/logs/backend.log

# Nginx日志
tail -f /var/log/nginx/social_media_access.log
tail -f /var/log/nginx/social_media_error.log

# Supervisor日志
supervisorctl tail -f social-media-backend
```

## 目录结构

```
/home/<USER>/social-media-management/
├── app/                    # 后端应用代码
├── frontend/               # 前端代码
│   └── build/             # 构建后的静态文件
├── venv/                  # Python虚拟环境
├── user_data/             # 用户数据目录
├── data/                  # 数据库文件目录
├── temp_downloads/        # 临时下载文件
├── logs/                  # 应用日志
├── configs/               # 配置文件模板
├── requirements.txt       # Python依赖
├── main.py               # 应用入口
├── .env                  # 环境变量配置
└── *.sh                  # 部署脚本
```

## 网络配置

### 端口说明
- **80**: Nginx HTTP服务
- **8000**: FastAPI后端服务（内部）
- **443**: HTTPS服务（可选）

### 防火墙配置
```bash
# 开放HTTP/HTTPS端口
firewall-cmd --permanent --add-service=http
firewall-cmd --permanent --add-service=https
firewall-cmd --reload

# 查看开放端口
firewall-cmd --list-services
```

## 性能优化

### 1. Nginx优化
- 启用Gzip压缩
- 配置静态文件缓存
- 优化代理缓冲区

### 2. Python优化
- 使用虚拟环境隔离依赖
- 配置合适的worker数量
- 启用日志轮转

### 3. 系统优化
- 配置合适的文件描述符限制
- 优化内存使用
- 定期清理临时文件

## 安全配置

### 1. 用户权限
- 使用专用的`web`用户运行应用
- 限制文件和目录权限
- 禁止root用户直接运行应用

### 2. 网络安全
- 配置防火墙规则
- 使用HTTPS（推荐）
- 限制API访问频率

### 3. 数据安全
- 定期备份数据库
- 保护敏感配置文件
- 监控异常访问

## 备份策略

### 1. 数据库备份
```bash
# 手动备份
cp /home/<USER>/social-media-management/data/social_media.db \
   /home/<USER>/social-media-management/data/social_media.db.backup.$(date +%Y%m%d_%H%M%S)

# 自动备份脚本（添加到crontab）
0 2 * * * /home/<USER>/backup-database.sh
```

### 2. 用户数据备份
```bash
# 打包用户数据
tar -czf /home/<USER>/backup_$(date +%Y%m%d_%H%M%S).tar.gz \
    -C /home/<USER>/social-media-management user_data data
```

## 监控和维护

### 1. 系统监控
- 监控CPU、内存使用率
- 监控磁盘空间
- 监控网络连接

### 2. 应用监控
- 监控服务运行状态
- 监控API响应时间
- 监控错误日志

### 3. 定期维护
- 清理临时文件
- 更新系统补丁
- 检查日志文件大小

## 故障排除

### 1. 服务无法启动
```bash
# 检查配置文件
nginx -t
supervisorctl status

# 查看详细错误
journalctl -u nginx -f
supervisorctl tail social-media-backend
```

### 2. 权限问题
```bash
# 重新设置权限
chown -R web:web /home/<USER>/social-media-management
chmod -R 755 /home/<USER>/social-media-management/user_data
```

### 3. 依赖问题
```bash
# 重新安装Python依赖
cd /home/<USER>/social-media-management
source venv/bin/activate
pip install -r requirements.txt

# 重新安装前端依赖
cd frontend
npm install
npm run build
```

## 更新部署

### 1. 代码更新
```bash
# 停止服务
supervisorctl stop social-media-backend

# 更新代码（如果使用git）
cd /home/<USER>/social-media-management
git pull

# 更新依赖
source venv/bin/activate
pip install -r requirements.txt

# 重新构建前端
cd frontend
npm install
npm run build

# 重启服务
supervisorctl start social-media-backend
```

### 2. 配置更新
```bash
# 更新Nginx配置后
nginx -t && systemctl reload nginx

# 更新Supervisor配置后
supervisorctl reread
supervisorctl update
```

## 联系支持

如果在部署过程中遇到问题，请检查：

1. **系统要求**: 确保CentOS 8版本正确
2. **网络连接**: 确保能够下载依赖包
3. **权限设置**: 确保用户权限配置正确
4. **端口占用**: 确保80和8000端口未被占用
5. **日志信息**: 查看详细的错误日志

## 总结

本部署方案提供了完整的CentOS 8原生部署解决方案，包括：

- ✅ 自动化部署脚本
- ✅ 详细的手动部署指南
- ✅ 完整的服务配置
- ✅ 监控和维护方案
- ✅ 故障排除指南

通过本方案，您可以在CentOS 8服务器上成功部署社交媒体管理系统，无需使用Docker容器化技术。
