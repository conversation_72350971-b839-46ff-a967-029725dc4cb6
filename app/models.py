from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, JSON, Float, Date, UniqueConstraint
from sqlalchemy.orm import relationship
from app.database import Base
from datetime import datetime
from zoneinfo import ZoneInfo

def beijing_now():
    return datetime.now(ZoneInfo("Asia/Shanghai"))

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    hashed_password = Column(String(255))
    created_at = Column(DateTime, default=beijing_now)

    accounts = relationship("PlatformAccount", back_populates="owner")
    feishu_apps = relationship("FeishuApp", back_populates="owner")
    download_records = relationship("DataDownloadRecord", back_populates="user")

class FeishuApp(Base):
    __tablename__ = "feishu_apps"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100))  # App名称
    app_id = Column(String(255))  # 飞书应用ID
    app_secret = Column(String(255))  # 飞书应用密钥
    user_id = Column(Integer, ForeignKey("users.id"))
    is_deleted = Column(Boolean, default=False)  # 删除标志
    created_at = Column(DateTime, default=beijing_now)
    updated_at = Column(DateTime, default=beijing_now, onupdate=beijing_now)

    # 多维表格相关字段（一对一关系）
    bitable_name = Column(String(255))  # 多维表格名称
    app_token = Column(String(255))  # 多维表格token
    folder_token = Column(String(255))  # 文件夹token
    url = Column(String(500))  # 访问URL
    bitable_created_at = Column(DateTime)  # 多维表格创建时间

    owner = relationship("User", back_populates="feishu_apps")
    platform_accounts = relationship("PlatformAccount", back_populates="feishu_app")
    feishu_tables = relationship("FeishuTable", back_populates="feishu_app")



class PlatformAccount(Base):
    __tablename__ = "platform_accounts"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100))
    platform = Column(String(50))  # wechat_mp, wechat_service, xiaohongshu, wechat_channels, douyin
    user_id = Column(Integer, ForeignKey("users.id"))
    login_status = Column(Boolean, default=False)
    last_login_time = Column(DateTime)
    cookies = Column(Text)  # 存储登录cookie
    created_at = Column(DateTime, default=beijing_now)

    # 飞书应用关联
    feishu_app_id = Column(Integer, ForeignKey("feishu_apps.id"))  # 关联的飞书应用

    owner = relationship("User", back_populates="accounts")
    data_records = relationship("DataRecord", back_populates="account")
    feishu_tables = relationship("FeishuTable", back_populates="account")
    feishu_app = relationship("FeishuApp", back_populates="platform_accounts")

class DataRecord(Base):
    __tablename__ = "data_records"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"))
    data_type = Column(String(50))  # user_summary, article_summary, etc.
    date = Column(DateTime)
    data = Column(JSON)  # 存储JSON格式的数据
    created_at = Column(DateTime, default=beijing_now)

    account = relationship("PlatformAccount", back_populates="data_records")

class FeishuTable(Base):
    __tablename__ = "feishu_tables"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"))
    feishu_app_id = Column(Integer, ForeignKey("feishu_apps.id"))  # 关联的飞书应用
    feishu_table_id = Column(String(255))   # 数据表ID
    feishu_table_name = Column(String(255)) # 数据表名称
    data_type = Column(String(50))          # 数据类型：content_trend, content_source, content_detail, user_channel
    feishu_record_ids = Column(JSON)        # 该表的记录ID列表
    created_at = Column(DateTime, default=beijing_now)
    updated_at = Column(DateTime, default=beijing_now, onupdate=beijing_now)

    account = relationship("PlatformAccount", back_populates="feishu_tables")
    feishu_app = relationship("FeishuApp", back_populates="feishu_tables")


# 微信公众号数据明细表
class WeChatMPContentTrend(Base):
    """微信公众号内容数据趋势明细表"""
    __tablename__ = "wechat_mp_content_trend"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"), nullable=False)
    date = Column(Date, nullable=False)  # 日期
    read_count = Column(Integer, default=0)  # 阅读次数
    read_user_count = Column(Integer, default=0)  # 阅读人数
    share_count = Column(Integer, default=0)  # 分享次数
    share_user_count = Column(Integer, default=0)  # 分享人数
    read_original_count = Column(Integer, default=0)  # 阅读原文次数
    read_original_user_count = Column(Integer, default=0)  # 阅读原文人数
    collect_count = Column(Integer, default=0)  # 收藏次数
    collect_user_count = Column(Integer, default=0)  # 收藏人数
    publish_count = Column(Integer, default=0)  # 群发篇数
    channel = Column(String(100))  # 渠道
    created_at = Column(DateTime, default=beijing_now)
    updated_at = Column(DateTime, default=beijing_now, onupdate=beijing_now)

    account = relationship("PlatformAccount")


class WeChatMPContentSource(Base):
    """微信公众号内容流量来源明细表"""
    __tablename__ = "wechat_mp_content_source"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"), nullable=False)
    channel = Column(String(100))  # 传播渠道
    publish_date = Column(Date)  # 发表日期
    title = Column(Text)  # 内容标题
    read_count = Column(Integer, default=0)  # 阅读次数
    read_ratio = Column(String(20))  # 阅读次数占比
    created_at = Column(DateTime, default=beijing_now)
    updated_at = Column(DateTime, default=beijing_now, onupdate=beijing_now)

    account = relationship("PlatformAccount")


class WeChatMPContentDetail(Base):
    """微信公众号内容已通知内容明细表"""
    __tablename__ = "wechat_mp_content_detail"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"), nullable=False)
    title = Column(Text)  # 内容标题
    publish_time = Column(DateTime)  # 发表时间
    total_read_user_count = Column(Integer, default=0)  # 总阅读人数
    total_read_count = Column(Integer, default=0)  # 总阅读次数
    total_share_user_count = Column(Integer, default=0)  # 总分享人数
    total_share_count = Column(Integer, default=0)  # 总分享次数
    follow_after_read_count = Column(Integer, default=0)  # 阅读后关注人数
    delivery_count = Column(Integer, default=0)  # 送达人数
    mp_message_read_count = Column(Integer, default=0)  # 公众号消息阅读次数
    delivery_read_rate = Column(String(20))  # 送达阅读率
    first_share_count = Column(Integer, default=0)  # 首次分享次数
    share_generated_read_count = Column(Integer, default=0)  # 分享产生阅读次数
    first_share_rate = Column(String(20))  # 首次分享率
    avg_read_per_share = Column(Integer, default=0)  # 每次分享带来阅读次数
    read_completion_rate = Column(String(20))  # 阅读完成率
    content_url = Column(Text)  # 内容url
    created_at = Column(DateTime, default=beijing_now)
    updated_at = Column(DateTime, default=beijing_now, onupdate=beijing_now)

    account = relationship("PlatformAccount")


class WeChatMPUserChannel(Base):
    """微信公众号用户增长表"""
    __tablename__ = "wechat_mp_user_channel"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"), nullable=False)
    date = Column(Date, nullable=False)  # 时间
    new_follow_count = Column(Integer, default=0)  # 新关注人数
    unfollow_count = Column(Integer, default=0)  # 取消关注人数
    net_follow_count = Column(Integer, default=0)  # 净增关注人数
    total_follow_count = Column(Integer, default=0)  # 累积关注人数
    created_at = Column(DateTime, default=beijing_now)
    updated_at = Column(DateTime, default=beijing_now, onupdate=beijing_now)

    account = relationship("PlatformAccount")


class WeChatMPUserSource(Base):
    """微信公众号用户来源数据表"""
    __tablename__ = "wechat_mp_user_source"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"), nullable=False)
    user_source = Column(Integer, nullable=False)  # 用户来源类型
    date = Column(Date, nullable=False)  # 日期
    new_user = Column(Integer, default=0)  # 新用户数
    cancel_user = Column(Integer, default=0)  # 取消用户数
    netgain_user = Column(Integer, default=0)  # 净增用户数
    cumulate_user = Column(Integer, default=0)  # 累计用户数
    created_at = Column(DateTime, default=beijing_now)
    updated_at = Column(DateTime, default=beijing_now, onupdate=beijing_now)

    account = relationship("PlatformAccount")

    # 添加唯一约束：同一账号、同一来源、同一日期只能有一条记录
    __table_args__ = (
        UniqueConstraint('account_id', 'user_source', 'date', name='uq_account_source_date'),
    )


class DataUpdateRecord(Base):
    __tablename__ = "data_update_records"

    id = Column(Integer, primary_key=True, index=True)
    start_date = Column(Date, nullable=False)  # 更新数据的开始日期
    end_date = Column(Date, nullable=False)    # 更新数据的结束日期
    status = Column(String(20), nullable=False, default='running')  # 'running', 'completed', 'failed'
    total_accounts = Column(Integer, default=0)  # 总账号数
    completed_accounts = Column(Integer, default=0)  # 已完成账号数
    current_account_name = Column(String(255))  # 当前处理的账号名
    current_step = Column(String(255))  # 当前执行步骤
    error_message = Column(Text)  # 错误信息
    created_at = Column(DateTime, default=beijing_now)  # 创建时间
    updated_at = Column(DateTime, default=beijing_now, onupdate=beijing_now)  # 更新时间
    completed_at = Column(DateTime)  # 完成时间

    # 关联关系
    task_items = relationship("DataUpdateTaskItem", back_populates="update_record", cascade="all, delete-orphan")


class DataUpdateTaskItem(Base):
    __tablename__ = "data_update_task_items"

    id = Column(Integer, primary_key=True, index=True)
    update_record_id = Column(Integer, ForeignKey("data_update_records.id"), nullable=False)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"), nullable=False)
    account_name = Column(String(100), nullable=False)
    platform = Column(String(50), nullable=False)  # wechat_channels, xiaohongshu, wechat_mp
    data_type = Column(String(50), nullable=False)  # single_video, follower_data, note_data, etc.
    data_type_display = Column(String(100))  # 显示名称
    status = Column(String(20), default='pending')  # pending, running, completed, failed, retrying
    error_message = Column(Text)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    created_at = Column(DateTime, default=beijing_now)
    updated_at = Column(DateTime, default=beijing_now, onupdate=beijing_now)

    # 关联关系
    update_record = relationship("DataUpdateRecord", back_populates="task_items")
    account = relationship("PlatformAccount")


class DataDownloadRecord(Base):
    __tablename__ = "data_download_records"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)  # 用户ID
    start_date = Column(Date, nullable=False)  # 下载数据的开始日期
    end_date = Column(Date, nullable=False)    # 下载数据的结束日期
    selected_accounts = Column(JSON)  # 选择的账号ID列表
    selected_data_types = Column(JSON)  # 选择的数据类型列表
    status = Column(String(20), nullable=False, default='running')  # 'running', 'completed', 'failed'
    total_files = Column(Integer, default=0)  # 总文件数
    completed_files = Column(Integer, default=0)  # 已完成文件数
    current_account_name = Column(String(255))  # 当前处理的账号名
    current_step = Column(String(255))  # 当前执行步骤
    download_path = Column(String(500))  # 下载文件夹路径
    zip_file_path = Column(String(500))  # ZIP文件路径
    error_message = Column(Text)  # 错误信息
    created_at = Column(DateTime, default=beijing_now)  # 创建时间
    updated_at = Column(DateTime, default=beijing_now, onupdate=beijing_now)  # 更新时间
    completed_at = Column(DateTime)  # 完成时间

    # 关联用户
    user = relationship("User", back_populates="download_records")


class LoginKeeperRecord(Base):
    __tablename__ = "login_keeper_records"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"), nullable=False)  # 账号ID
    platform = Column(String(50), nullable=False)  # 平台类型
    visited_page = Column(String(500))  # 访问的页面URL
    page_title = Column(String(255))  # 页面标题
    status = Column(String(20), nullable=False)  # 'success', 'failed', 'login_expired'
    error_message = Column(Text)  # 错误信息
    response_time = Column(Float)  # 响应时间（秒）
    created_at = Column(DateTime, default=beijing_now)  # 创建时间

    # 关联账号
    account = relationship("PlatformAccount")


class WeChatChannelsVideoData(Base):
    """微信视频号单篇视频数据表"""
    __tablename__ = "wechat_channels_video_data"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"), nullable=False)
    video_description = Column(Text)  # 视频描述
    video_id = Column(String(110))  # 视频ID
    publish_time = Column(DateTime)  # 发布时间
    completion_rate = Column(String(20))  # 完播率
    avg_play_duration = Column(Integer, default=0)  # 平均播放时长(秒)
    play_count = Column(Integer, default=0)  # 播放量
    recommend_count = Column(Integer, default=0)  # 推荐
    like_count = Column(Integer, default=0)  # 喜欢
    comment_count = Column(Integer, default=0)  # 评论量
    share_count = Column(Integer, default=0)  # 分享量
    follow_count = Column(Integer, default=0)  # 关注量
    forward_chat_moments = Column(Integer, default=0)  # 转发聊天和朋友圈
    set_as_ringtone = Column(Integer, default=0)  # 设为铃声
    set_as_status = Column(Integer, default=0)  # 设为状态
    set_as_moments_cover = Column(Integer, default=0)  # 设为朋友圈封面
    created_at = Column(DateTime, default=beijing_now)
    updated_at = Column(DateTime, default=beijing_now, onupdate=beijing_now)

    account = relationship("PlatformAccount")

    # 添加唯一约束：同一账号、同一视频ID、同一发布时间只能有一条记录
    __table_args__ = (
        UniqueConstraint('account_id', 'video_id', 'publish_time', name='uq_account_video_time'),
    )


class XiaohongshuNoteData(Base):
    """小红书笔记数据表"""
    __tablename__ = "xiaohongshu_note_data"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"), nullable=False)
    note_title = Column(String(500))  # 笔记标题（MySQL兼容性：限制长度以支持唯一约束）
    first_publish_time = Column(DateTime)  # 首次发布时间
    content_type = Column(String(20))  # 体裁（图文/视频等）
    view_count = Column(Integer, default=0)  # 观看量
    like_count = Column(Integer, default=0)  # 点赞
    comment_count = Column(Integer, default=0)  # 评论
    collect_count = Column(Integer, default=0)  # 收藏
    follow_count = Column(Integer, default=0)  # 涨粉
    share_count = Column(Integer, default=0)  # 分享
    avg_view_duration = Column(Float, default=0.0)  # 人均观看时长（秒）
    barrage_count = Column(Integer, default=0)  # 弹幕
    created_at = Column(DateTime, default=beijing_now)
    updated_at = Column(DateTime, default=beijing_now, onupdate=beijing_now)

    account = relationship("PlatformAccount")

    # 添加唯一约束：同一账号、同一笔记标题、同一首次发布时间只能有一条记录
    __table_args__ = (
        UniqueConstraint('account_id', 'note_title', 'first_publish_time', name='uq_account_note_time'),
    )


class XiaohongshuAccountOverview(Base):
    """小红书账号概览数据表"""
    __tablename__ = "xiaohongshu_account_overview"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"), nullable=False)
    date = Column(Date, nullable=False)  # 数据日期

    # 各种趋势数据
    view_count = Column(Integer, default=0)  # 观看量
    view_time_count = Column(Integer, default=0)  # 观看总时长
    home_view_count = Column(Integer, default=0)  # 主页访客量
    like_count = Column(Integer, default=0)  # 点赞数
    collect_count = Column(Integer, default=0)  # 收藏数
    comment_count = Column(Integer, default=0)  # 评论数
    danmaku_count = Column(Integer, default=0)  # 弹幕数
    rise_fans_count = Column(Integer, default=0)  # 涨粉数
    share_count = Column(Integer, default=0)  # 分享数

    created_at = Column(DateTime, default=beijing_now)
    updated_at = Column(DateTime, default=beijing_now, onupdate=beijing_now)

    account = relationship("PlatformAccount")

    # 添加唯一约束：同一账号、同一日期只能有一条记录
    __table_args__ = (
        UniqueConstraint('account_id', 'date', name='uq_account_date'),
    )


class XiaohongshuFansData(Base):
    """小红书粉丝数据表"""
    __tablename__ = "xiaohongshu_fans_data"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"), nullable=False)
    date = Column(Date, nullable=False)  # 数据日期

    # 粉丝相关数据
    total_fans_count = Column(Integer, default=0)  # 总关注数
    new_fans_count = Column(Integer, default=0)  # 新增关注数
    unfans_count = Column(Integer, default=0)  # 取关数

    created_at = Column(DateTime, default=beijing_now)
    updated_at = Column(DateTime, default=beijing_now, onupdate=beijing_now)

    account = relationship("PlatformAccount")

    # 添加唯一约束：同一账号、同一日期只能有一条记录
    __table_args__ = (
        UniqueConstraint('account_id', 'date', name='uq_fans_account_date'),
    )


class WeChatChannelsFollowerData(Base):
    """微信视频号关注者数据表"""
    __tablename__ = "wechat_channels_follower_data"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"), nullable=False)
    date = Column(Date, nullable=False)  # 数据日期

    # 关注者相关数据
    net_follower_increase = Column(Integer, default=0)  # 净增关注
    new_followers = Column(Integer, default=0)  # 新增关注
    unfollowers = Column(Integer, default=0)  # 取消关注
    total_followers = Column(Integer, default=0)  # 关注者总数

    created_at = Column(DateTime, default=beijing_now)
    updated_at = Column(DateTime, default=beijing_now, onupdate=beijing_now)

    account = relationship("PlatformAccount")

    # 添加唯一约束：同一账号、同一日期只能有一条记录
    __table_args__ = (
        UniqueConstraint('account_id', 'date', name='uq_follower_account_date'),
    )


class AutoUpdateConfig(Base):
    """自动更新配置表"""
    __tablename__ = "auto_update_config"

    id = Column(Integer, primary_key=True, index=True)
    enabled = Column(Boolean, default=False)  # 是否启用自动更新
    update_time = Column(String(5), default="02:00")  # 更新时间 (HH:MM)
    update_days = Column(Integer, default=30)  # 更新天数
    last_update = Column(DateTime, nullable=True)  # 上次更新时间

    created_at = Column(DateTime, default=beijing_now)
    updated_at = Column(DateTime, default=beijing_now, onupdate=beijing_now)