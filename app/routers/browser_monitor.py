"""
浏览器资源监控API
"""
from fastapi import APIRouter, Depends
from app.services.browser_manager import browser_manager
from app.routers.auth import get_current_user

router = APIRouter()


@router.get("/browser/status")
async def get_browser_status(current_user=Depends(get_current_user)):
    """获取浏览器资源使用状态"""
    try:
        status = browser_manager.get_resource_status()
        return {
            "success": True,
            "data": status
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


@router.post("/browser/cleanup")
async def cleanup_stale_contexts(
    max_age_seconds: int = 3600,
    current_user=Depends(get_current_user)
):
    """清理长时间未使用的浏览器上下文"""
    try:
        await browser_manager.cleanup_stale_contexts(max_age_seconds)
        status = browser_manager.get_resource_status()
        return {
            "success": True,
            "message": "清理完成",
            "data": status
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


@router.get("/browser/log")
async def log_active_contexts(current_user=Depends(get_current_user)):
    """记录当前活跃的浏览器上下文到日志"""
    try:
        browser_manager._log_active_contexts()
        return {
            "success": True,
            "message": "已记录到日志"
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }
