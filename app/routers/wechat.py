from fastapi import APIRouter, Depends, HTTPException, Response
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from pydantic import BaseModel
from typing import Optional, List
import io
from app.database import get_db
from app.services.wechat_service import WeChatMPService
from app.services.wechat_channels_service import WeChatChannelsService
from app.services.xiaohongshu_service import XiaohongshuService
from app.models import PlatformAccount, DataRecord
from app.routers.auth import get_current_user
import asyncio
import json

router = APIRouter()

# 存储活跃的登录会话
login_sessions = {}

# Pydantic模型
class DataCollectionRequest(BaseModel):
    start_date: str  # YYYY-MM-DD格式
    end_date: str    # YYYY-MM-DD格式
    data_type: str   # user_summary, article_summary

class DataDownloadRequest(BaseModel):
    start_date: str  # YYYY-MM-DD格式
    end_date: str    # YYYY-MM-DD格式
    busi: Optional[int] = 3  # 业务类型，默认3（向后兼容）
    tmpl: Optional[int] = 19  # 模板类型，默认19（向后兼容）
    data_type: Optional[str] = None  # 数据类型：content_trend, content_source, content_detail, user_channel

@router.post("/login/refresh_qrcode/{account_id}")
async def refresh_login_qrcode(
    account_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """刷新登录二维码"""
    # 检查账号是否存在
    account = db.query(PlatformAccount).filter(PlatformAccount.id == account_id).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")

    # 检查是否有现有的登录会话
    if account_id not in login_sessions:
        raise HTTPException(status_code=400, detail="没有活跃的登录会话，请先获取二维码")

    existing_service = login_sessions[account_id]

    # 调用刷新二维码方法
    try:
        if hasattr(existing_service, 'refresh_qrcode'):
            refreshed_qr = await existing_service.refresh_qrcode()
            if refreshed_qr:
                return {"qrcode": refreshed_qr, "account_id": account_id}
            else:
                raise HTTPException(status_code=500, detail="刷新二维码失败")
        else:
            raise HTTPException(status_code=400, detail="该平台不支持刷新二维码")
    except Exception as e:
        print(f"刷新二维码异常: {e}")
        raise HTTPException(status_code=500, detail=f"刷新二维码失败: {str(e)}")

@router.post("/login/qrcode/{account_id}")
async def get_login_qrcode(
    account_id: int,
    headless: bool = True,  # 添加headless参数，默认为True（无头模式）
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取登录二维码（支持微信公众号、视频号、小红书和抖音）"""

    # 查询账号（确保属于当前用户）
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")

    # 若已存在登录会话，优先返回缓存的或在现有页面重截的二维码，避免重复创建上下文
    # if account_id in login_sessions:
    #     existing_service = login_sessions[account_id]
    #     # 统一复用已有会话（视频号/公众号/小红书），避免重复创建上下文
    #     try:
    #         if hasattr(existing_service, 'get_cached_or_rescreenshot_qrcode'):
    #             cached_qr = await existing_service.get_cached_or_rescreenshot_qrcode()
    #             if cached_qr:
    #                 return {"qrcode": cached_qr, "account_id": account_id}
    #     except Exception:
    #         # 忽略重截失败，继续走新建流程
    #         pass

    # 根据平台类型选择对应的服务类
    if account.platform == "wechat_channels":
        service = WeChatChannelsService(account_id=account_id, headless=headless)
    elif account.platform in ["wechat_mp", "wechat_service"]:
        service = WeChatMPService(account_id=account_id, headless=headless)
    elif account.platform == "xiaohongshu":
        service = XiaohongshuService(account_id=account_id, headless=headless)
    elif account.platform == "douyin":
        from app.services.douyin_service import DouyinService
        service = DouyinService(account_id=account_id)
    else:
        raise HTTPException(status_code=400, detail=f"不支持的平台类型: {account.platform}")

    # 新建服务后，若有缓存或可在现有页面重截，也优先使用
    # if hasattr(service, 'get_cached_or_rescreenshot_qrcode'):
    #     cached_qr2 = await service.get_cached_or_rescreenshot_qrcode()
    #     if cached_qr2:
    #         login_sessions[account_id] = service
    #         return {"qrcode": cached_qr2, "account_id": account_id}

    qr_code = await service.get_login_qrcode()

    if not qr_code:
        raise HTTPException(status_code=500, detail="获取二维码失败")

    # 保存会话
    login_sessions[account_id] = service

    return {"qrcode": qr_code, "account_id": account_id}

@router.get("/login/status/{account_id}")
async def check_login_status(
    account_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """检查登录状态（支持微信公众号、视频号、小红书和抖音）"""

    if account_id not in login_sessions:
        raise HTTPException(status_code=404, detail="登录会话不存在")

    service = login_sessions[account_id]
    is_logged_in = await service.check_login_status()

    if is_logged_in:
        try:
            # 保存登录状态到文件（重要！）
            await service.save_login_state()

            # 获取cookies并保存
            cookies = await service.get_cookies()

            # 更新数据库
            account = db.query(PlatformAccount).filter(PlatformAccount.id == account_id).first()
            account.login_status = True
            account.cookies = cookies
            account.last_login_time = datetime.utcnow()
            db.commit()

        except Exception as e:
            print(f"保存登录状态时出错: {e}")

        finally:
            # 无论如何都要清理会话
            try:
                await service.close()
            except Exception as e:
                print(f"关闭服务时出错: {e}")

            login_sessions.pop(account_id, None)  # 安全删除，避免KeyError

    return {"logged_in": is_logged_in}

@router.post("/collect-data/{account_id}")
async def collect_data(
    account_id: int,
    request: DataCollectionRequest,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """采集微信公众号数据"""

    # 查询账号（确保属于当前用户）
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")

    # 检查账号是否已登录
    if not account.login_status:
        raise HTTPException(status_code=400, detail="账号未登录，请先登录")

    try:
        # 创建微信服务实例并恢复登录状态（传入账号ID）
        wechat_service = WeChatMPService(account_id=account_id)
        
        # 尝试加载已保存的登录状态
        if not await wechat_service.load_login_state():
            # 如果加载状态失败，说明登录状态文件丢失或过期
            # 更新数据库状态为未登录
            account.login_status = False
            account.cookies = None
            account.last_login_time = None
            db.commit()
            print(f"登录状态文件不存在或已过期，已重置账号 {account_id} 的登录状态")
            raise HTTPException(
                status_code=400, 
                detail="登录会话已过期，请重新获取二维码登录"
            )

        collected_data = None

        if request.data_type == "user_summary":
            collected_data = await wechat_service.get_user_summary_data(
                request.start_date, request.end_date
            )
        elif request.data_type == "article_summary":
            collected_data = await wechat_service.get_article_summary_data(
                request.start_date, request.end_date
            )
        else:
            raise HTTPException(status_code=400, detail="不支持的数据类型")

        if collected_data is None:
            raise HTTPException(status_code=500, detail="数据采集失败")

        # 保存数据到数据库
        data_record = DataRecord(
            account_id=account_id,
            data_type=request.data_type,
            date=datetime.strptime(request.start_date, "%Y-%m-%d"),
            data=collected_data
        )
        db.add(data_record)
        db.commit()
        db.refresh(data_record)

        await wechat_service.close()

        return {
            "message": "数据采集成功",
            "data_record_id": data_record.id,
            "data": collected_data
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据采集失败: {str(e)}")

@router.get("/data/{account_id}")
async def get_collected_data(
    account_id: int,
    data_type: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取已采集的数据"""

    # 查询账号（确保属于当前用户）
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")

    # 构建查询条件
    query = db.query(DataRecord).filter(DataRecord.account_id == account_id)

    if data_type:
        query = query.filter(DataRecord.data_type == data_type)

    if start_date:
        query = query.filter(DataRecord.date >= datetime.strptime(start_date, "%Y-%m-%d"))

    if end_date:
        query = query.filter(DataRecord.date <= datetime.strptime(end_date, "%Y-%m-%d"))

    # 按日期降序排列
    data_records = query.order_by(DataRecord.date.desc()).all()

    return {
        "account_id": account_id,
        "account_name": account.name,
        "data_records": [
            {
                "id": record.id,
                "data_type": record.data_type,
                "date": record.date.strftime("%Y-%m-%d"),
                "data": record.data,
                "created_at": record.created_at
            }
            for record in data_records
        ]
    }

class BatchDownloadRequest(BaseModel):
    start_date: str  # YYYY-MM-DD格式
    end_date: str    # YYYY-MM-DD格式
    data_types: List[str]  # 数据类型列表

@router.post("/download-data/{account_id}")
async def download_data_excel(
    account_id: int,
    request: DataDownloadRequest,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """下载单个微信公众号数据Excel文件到服务器本地存储（兼容旧接口）"""
    # 转换为批量下载请求
    batch_request = BatchDownloadRequest(
        start_date=request.start_date,
        end_date=request.end_date,
        data_types=[request.data_type] if request.data_type else ["content_detail"]
    )
    return await batch_download_data_excel(account_id, batch_request, current_user, db)

# 存储下载任务状态
download_tasks = {}

@router.post("/batch-download-data/{account_id}")
async def batch_download_data_excel(
    account_id: int,
    request: BatchDownloadRequest,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """批量下载数据Excel文件到服务器本地存储（支持微信公众号、视频号和小红书）"""
    print(f"批量下载请求详情: account_id={account_id}, request={request}")

    # 查询账号（确保属于当前用户）
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    if not account:
        print(f"账号不存在: account_id={account_id}, user_id={current_user.id}")
        raise HTTPException(status_code=404, detail="账号不存在")

    print(f"找到账号: {account.name}, 登录状态: {account.login_status}")

    # 检查账号是否已登录
    if not account.login_status:
        print("账号未登录")
        raise HTTPException(status_code=400, detail="账号未登录，请先登录")

    # 检查是否已绑定飞书应用
    if not account.feishu_app_id:
        raise HTTPException(status_code=400, detail="请先绑定飞书应用")

    # 生成任务ID
    import uuid
    task_id = str(uuid.uuid4())

    # 初始化任务状态
    download_tasks[task_id] = {
        "status": "running",
        "progress": 0,
        "total": len(request.data_types),
        "current_file": "",
        "downloaded_files": [],
        "failed_files": [],
        "message": "开始下载任务..."
    }

    # 启动后台任务
    import asyncio
    asyncio.create_task(execute_download_task(task_id, account_id, request, current_user.id))

    # 立即返回任务ID
    return {
        "success": True,
        "task_id": task_id,
        "message": "下载任务已启动，请使用任务ID查询进度"
    }

async def execute_download_task(task_id: str, account_id: int, request: BatchDownloadRequest, user_id: int):
    """执行下载任务的后台函数"""
    try:
        # 重新获取数据库连接
        from app.database import SessionLocal
        db = SessionLocal()

        # 查询账号
        account = db.query(PlatformAccount).filter(
            PlatformAccount.id == account_id,
            PlatformAccount.user_id == user_id
        ).first()

        if not account:
            download_tasks[task_id]["status"] = "failed"
            download_tasks[task_id]["message"] = "账号不存在"
            return

        # 导入Excel解析工具
        from app.utils.excel_parser import ExcelDataParser
        parser = ExcelDataParser()

        # 获取存储目录
        storage_dir = parser.get_storage_directory(account.feishu_app_id, account_id)

        # 清理存储目录（只在批量下载开始时清理一次）
        if not parser.clear_directory(storage_dir):
            download_tasks[task_id]["status"] = "failed"
            download_tasks[task_id]["message"] = "清理存储目录失败"
            return

        # 创建或获取服务实例（只创建一次）
        if account_id in login_sessions:
            service = login_sessions[account_id]
        else:
            # 根据平台类型创建对应的服务实例
            if account.platform == "xiaohongshu":
                service = XiaohongshuService(account_id=account_id)
            elif account.platform == "wechat_channels":
                service = WeChatChannelsService(account_id=account_id)
            else:
                # 默认为微信公众号/服务号
                service = WeChatMPService(account_id=account_id)

            # 尝试加载已保存的登录状态
            if not await service.load_login_state():
                # 如果加载状态失败，说明登录状态文件丢失或过期
                # 更新数据库状态为未登录
                account.login_status = False
                account.cookies = None
                account.last_login_time = None
                db.commit()
                print(f"登录状态文件不存在或已过期，已重置账号 {account_id} 的登录状态")
                download_tasks[task_id]["status"] = "failed"
                download_tasks[task_id]["message"] = "登录会话已过期，请重新获取二维码登录"
                return
            # 保存到会话中，避免重复创建
            login_sessions[account_id] = service

        downloaded_files = []
        failed_files = []

        print(f"开始批量下载 {len(request.data_types)} 个文件")

        for i, data_type in enumerate(request.data_types):
            try:
                print(f"下载文件 {i+1}/{len(request.data_types)}: {data_type}")

                # 根据平台类型和数据类型下载数据
                excel_data = None
                if account.platform == "xiaohongshu":
                    if data_type == "note_data":
                        excel_data = await service.download_note_data_excel(
                            begin_date=request.start_date,
                            end_date=request.end_date
                        )
                    else:
                        failed_files.append({"data_type": data_type, "error": f"小红书平台不支持数据类型: {data_type}"})
                        continue
                else:
                    # 微信平台（公众号、服务号、视频号）
                    excel_data = await service.download_data_excel(
                        begin_date=request.start_date,
                        end_date=request.end_date,
                        data_type=data_type
                    )

                if not excel_data:
                    failed_files.append({"data_type": data_type, "error": "下载数据失败"})
                    continue

                # 生成文件名，包含平台、账号ID和数据类型
                platform_prefix = account.platform if account.platform else "wechat"
                filename = f"{platform_prefix}_data_account_{account_id}_{data_type}_{request.start_date}_to_{request.end_date}.xlsx"

                # 保存文件到本地存储
                if not parser.save_excel_file(storage_dir, filename, excel_data):
                    failed_files.append({"data_type": data_type, "error": "保存文件失败"})
                    continue

                downloaded_files.append({
                    "data_type": data_type,
                    "filename": filename,
                    "file_size": len(excel_data),
                    "storage_path": f"{storage_dir}/{filename}"
                })

                print(f"文件已保存到: {storage_dir}/{filename}")

                # 如果不是最后一个文件，等待2-5秒
                if i < len(request.data_types) - 1:
                    import asyncio
                    import random
                    wait_time = 2 + random.random() * 3  # 2-5秒随机等待
                    print(f"等待 {wait_time:.1f} 秒后下载下一个文件...")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                print(f"下载 {data_type} 失败: {e}")
                failed_files.append({"data_type": data_type, "error": str(e)})

                # 更新任务进度
                download_tasks[task_id]["progress"] = i + 1
                download_tasks[task_id]["current_file"] = data_type
                download_tasks[task_id]["downloaded_files"] = downloaded_files
                download_tasks[task_id]["failed_files"] = failed_files

            except Exception as e:
                print(f"下载 {data_type} 失败: {e}")
                failed_files.append({"data_type": data_type, "error": str(e)})
                download_tasks[task_id]["failed_files"] = failed_files

        # 下载完成后，获取用户来源数据（仅限微信平台）
        user_source_result = None
        if account.platform in ["wechat_mp", "wechat_service"]:
            try:
                print("开始获取用户来源数据...")
                download_tasks[task_id]["message"] = "正在获取用户来源数据..."

                # 获取用户来源数据
                user_source_data = await service.fetch_user_source_data(
                    begin_date=request.start_date,
                    end_date=request.end_date
                )

                if user_source_data:
                    # 解析并保存用户来源数据
                    user_source_result = service.parse_and_save_user_source_data(
                        user_source_data, account_id
                    )

                    if user_source_result.get("success"):
                        print(f"用户来源数据获取成功: {user_source_result}")
                    else:
                        print(f"用户来源数据保存失败: {user_source_result.get('error')}")
                else:
                    print("未能获取到用户来源数据")

            except Exception as e:
                print(f"获取用户来源数据时出错: {e}")
                user_source_result = {"success": False, "error": str(e)}
        else:
            print(f"平台 {account.platform} 不支持用户来源数据获取")

        # 任务完成
        download_tasks[task_id]["status"] = "completed"

        # 构建完成消息
        message_parts = [f"批量下载完成，成功: {len(downloaded_files)} 个，失败: {len(failed_files)} 个"]
        if user_source_result:
            if user_source_result.get("success"):
                message_parts.append(f"用户来源数据: 新增 {user_source_result.get('imported_count', 0)} 条，更新 {user_source_result.get('updated_count', 0)} 条")
            else:
                message_parts.append(f"用户来源数据获取失败: {user_source_result.get('error', '未知错误')}")

        download_tasks[task_id]["message"] = "；".join(message_parts)
        download_tasks[task_id]["downloaded_files"] = downloaded_files
        download_tasks[task_id]["failed_files"] = failed_files
        download_tasks[task_id]["user_source_result"] = user_source_result

        db.close()

    except Exception as e:
        print(f"下载任务失败: {e}")
        download_tasks[task_id]["status"] = "failed"
        download_tasks[task_id]["message"] = f"下载任务失败: {str(e)}"

@router.get("/download-task-status/{task_id}")
async def get_download_task_status(task_id: str):
    """查询下载任务状态"""
    if task_id not in download_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    return {
        "success": True,
        "task_status": download_tasks[task_id]
    }

@router.get("/download-data/{account_id}")
async def download_data_excel_get(
    account_id: int,
    start_date: str,
    end_date: str,
    busi: int = 3,
    tmpl: int = 19,
    data_type: Optional[str] = None,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """GET方式下载微信公众号数据Excel文件（用于前端直接链接）"""
    print(f"收到下载请求: account_id={account_id}, start_date={start_date}, end_date={end_date}, data_type={data_type}")

    # 调用POST版本的下载方法
    request = DataDownloadRequest(
        start_date=start_date,
        end_date=end_date,
        busi=busi,
        tmpl=tmpl,
        data_type=data_type
    )
    
    return await download_data_excel(account_id, request, current_user, db)

@router.get("/download-data-types")
async def get_download_data_types():
    """获取可用的数据下载类型"""
    from app.services.wechat_service import WeChatMPService

    templates = WeChatMPService.DOWNLOAD_TEMPLATES
    data_types = []

    for key, config in templates.items():
        data_types.append({
            "key": key,
            "name": config.get("name", key),
            "description": f"包含 {len(config.get('fields', []))} 个字段"
        })

    return {
        "success": True,
        "data_types": data_types
    }

@router.post("/logout/{account_id}")
async def logout_account(
    account_id: int,
    clear_saved_state: bool = True,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """注销指定微信公众号账号的登录状态"""
    print(f"收到注销请求: account_id={account_id}, clear_saved_state={clear_saved_state}")
    
    # 查询账号（确保属于当前用户）
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")
    
    try:
        logout_success = False
        
        # 如果有活跃的登录会话，使用它进行注销
        if account_id in login_sessions:
            print(f"找到活跃的登录会话，开始注销...")
            wechat_service = login_sessions[account_id]
            logout_success = await wechat_service.logout(clear_saved_state=clear_saved_state)
            
            # 从活跃会话中移除
            login_sessions.pop(account_id, None)  # 安全删除，避免KeyError
            print(f"已从活跃会话中移除账号 {account_id}")
        else:
            # 没有活跃会话，但可能有保存的登录状态
            print(f"没有活跃会话，尝试清理保存的登录状态...")
            wechat_service = WeChatMPService(account_id=account_id)
            
            # 只清理保存的状态文件
            if clear_saved_state:
                wechat_service._clear_saved_login_state()
                logout_success = True
            else:
                logout_success = True
        
        # 更新数据库中的账号状态
        account.login_status = False  # 重要：更新登录状态
        account.cookies = None  # 清除保存的cookies
        account.last_login_time = None  # 清除最后登录时间
        db.commit()
        
        return {
            "success": logout_success,
            "message": "注销成功" if logout_success else "注销完成，但可能存在部分问题",
            "account_id": account_id,
            "cleared_saved_state": clear_saved_state
        }
        
    except Exception as e:
        print(f"注销过程中发生错误: {e}")
        
        # 即使出错也要尝试清理会话
        login_sessions.pop(account_id, None)  # 安全删除，避免KeyError
        
        # 尝试强制清理
        try:
            wechat_service = WeChatMPService(account_id=account_id)
            await wechat_service.force_logout()
        except:
            pass
        
        raise HTTPException(status_code=500, detail=f"注销失败: {str(e)}")

@router.post("/force-logout/{account_id}")
async def force_logout_account(
    account_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """强制注销指定微信公众号账号（忽略所有错误）"""
    print(f"收到强制注销请求: account_id={account_id}")
    
    # 查询账号（确保属于当前用户）
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")
    
    try:
        # 清理活跃会话
        if account_id in login_sessions:
            try:
                wechat_service = login_sessions[account_id]
                await wechat_service.force_logout()
            except:
                pass
            login_sessions.pop(account_id, None)  # 安全删除，避免KeyError
            print(f"已强制清理活跃会话: {account_id}")
        
        # 强制清理保存的状态
        try:
            wechat_service = WeChatMPService(account_id=account_id)
            await wechat_service.force_logout()
        except:
            pass
        
        # 更新数据库状态
        account.login_status = False  # 重要：更新登录状态
        account.cookies = None  # 清除保存的cookies
        account.last_login_time = None  # 清除最后登录时间
        db.commit()
        
        return {
            "success": True,
            "message": "强制注销完成",
            "account_id": account_id
        }
        
    except Exception as e:
        print(f"强制注销过程中发生错误: {e}")
        # 强制注销不应该失败
        return {
            "success": True,
            "message": f"强制注销完成（部分清理可能失败: {str(e)}）",
            "account_id": account_id
        }

@router.get("/logout-all")
async def logout_all_accounts(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """注销当前用户的所有微信公众号账号"""
    print(f"收到注销所有账号请求: user_id={current_user.id}")
    
    # 查询用户的所有账号
    accounts = db.query(PlatformAccount).filter(
        PlatformAccount.user_id == current_user.id,
        PlatformAccount.platform == "wechat_mp"
    ).all()
    
    if not accounts:
        return {
            "success": True,
            "message": "没有找到需要注销的账号",
            "logout_results": []
        }
    
    logout_results = []
    
    for account in accounts:
        try:
            print(f"注销账号 {account.id}...")
            
            # 清理活跃会话
            if account.id in login_sessions:
                try:
                    wechat_service = login_sessions[account.id]
                    await wechat_service.logout(clear_saved_state=True)
                except Exception as e:
                    print(f"注销账号 {account.id} 失败: {e}")
                login_sessions.pop(account.id, None)  # 安全删除，避免KeyError
            
            # 清理保存的状态
            try:
                wechat_service = WeChatMPService(account_id=account.id)
                wechat_service._clear_saved_login_state()
            except:
                pass
            
            # 更新数据库状态
            account.login_status = False  # 重要：更新登录状态
            account.cookies = None  # 清除保存的cookies
            account.last_login_time = None  # 清除最后登录时间
            
            logout_results.append({
                "account_id": account.id,
                "account_name": account.account_name,
                "success": True,
                "message": "注销成功"
            })
            
        except Exception as e:
            print(f"注销账号 {account.id} 过程中发生错误: {e}")
            logout_results.append({
                "account_id": account.id,
                "account_name": account.account_name,
                "success": False,
                "message": f"注销失败: {str(e)}"
            })
    
    db.commit()
    
    success_count = sum(1 for result in logout_results if result["success"])
    total_count = len(logout_results)
    
    return {
        "success": success_count == total_count,
        "message": f"批量注销完成: {success_count}/{total_count} 个账号注销成功",
        "logout_results": logout_results
    }