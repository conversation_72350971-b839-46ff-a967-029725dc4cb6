import asyncio
import logging
import os
import time
from typing import Optional, Dict, Any
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>rowserContext, Page

logger = logging.getLogger(__name__)


class BrowserManager:
    """全局浏览器管理器：
    - 维护单个 Playwright 和 Browser 实例
    - 通过创建/关闭 BrowserContext 实现账号级隔离
    - 控制并发、提供容灾重启
    - 支持优先级和超时机制
    """

    _instance: Optional["BrowserManager"] = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        self.playwright = None
        self.browser: Optional[Browser] = None
        self._lock = asyncio.Lock()

        # 增加并发限制，提供更多资源
        max_contexts = int(os.getenv("MAX_BROWSER_CONTEXTS", "8"))  # 从4增加到8
        self._semaphore = asyncio.Semaphore(max_contexts)

        # 为用户交互操作预留的高优先级资源池
        priority_contexts = int(os.getenv("PRIORITY_BROWSER_CONTEXTS", "2"))
        self._priority_semaphore = asyncio.Semaphore(priority_contexts)

        # 资源监控
        self._active_contexts: Dict[str, Dict[str, Any]] = {}
        self._context_counter = 0

        logger.info(f"BrowserManager初始化: 总上下文={max_contexts}, 优先级上下文={priority_contexts}")

    async def start(self):
        async with self._lock:
            if self.playwright and self.browser:
                return
            logger.info("启动全局Playwright与浏览器 ...")
            self.playwright = await async_playwright().start()
            args = [
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-gpu",
                "--disable-extensions",
                "--disable-background-networking",
                "--disable-renderer-backgrounding",
                "--mute-audio",
            ]
            self.browser = await self.playwright.chromium.launch(
                headless=True,
                args=args,
                timeout=30000,
                ignore_default_args=['--enable-automation', '--enable-blink-features=IdleDetection']
            )
            logger.info("全局浏览器已启动")

    async def stop(self):
        async with self._lock:
            logger.info("停止全局浏览器与Playwright ...")
            try:
                if self.browser:
                    # 强制关闭所有上下文
                    contexts = self.browser.contexts
                    for context in contexts:
                        try:
                            await context.close()
                        except Exception as e:
                            logger.warning(f"强制关闭上下文出错: {e}")

                    await self.browser.close()
                    self.browser = None
                    logger.info("浏览器已关闭")
            except Exception as e:
                logger.warning(f"关闭浏览器出错: {e}")

            try:
                if self.playwright:
                    await self.playwright.stop()
                    self.playwright = None
                    logger.info("Playwright已停止")
            except Exception as e:
                logger.warning(f"停止Playwright出错: {e}")

            # 等待一下确保资源完全释放
            await asyncio.sleep(0.5)
            logger.info("全局浏览器已停止")

    async def _ensure_running(self):
        if not self.playwright or not self.browser:
            await self.start()

    async def create_context(self, *, user_agent: Optional[str] = None, viewport: Optional[dict] = None,
                             storage_state: Optional[dict] = None, accept_downloads: bool = True,
                             priority: bool = False, timeout: float = 60.0,
                             operation_type: str = "unknown") -> BrowserContext:
        """创建浏览器上下文

        Args:
            user_agent: 用户代理
            viewport: 视口大小
            storage_state: 存储状态
            accept_downloads: 是否接受下载
            priority: 是否为高优先级操作（如用户交互）
            timeout: 获取资源的超时时间（秒）
            operation_type: 操作类型，用于监控和调试
        """
        await self._ensure_running()

        context_id = f"ctx_{self._context_counter}"
        self._context_counter += 1

        # 记录上下文创建请求
        logger.info(f"请求创建上下文 {context_id}: type={operation_type}, priority={priority}")

        # 检查是否为登录二维码类型，如果是则清理同类型的现有上下文
        if "login_qrcode" in operation_type:
            await self._cleanup_login_qrcode_contexts(operation_type)

        # 选择合适的信号量
        semaphore = self._priority_semaphore if priority else self._semaphore

        try:
            # 带超时的信号量获取
            await asyncio.wait_for(semaphore.acquire(), timeout=timeout)
        except asyncio.TimeoutError:
            logger.error(f"获取浏览器上下文超时 {context_id}: type={operation_type}, timeout={timeout}s")
            logger.info(f"当前活跃上下文数量: {len(self._active_contexts)}")
            self._log_active_contexts()
            raise Exception(f"获取浏览器资源超时({timeout}s)，请稍后重试")

        try:
            context = await self.browser.new_context(
                storage_state=storage_state,
                accept_downloads=accept_downloads,
                user_agent=user_agent or 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120 Safari/537.36',
                viewport=viewport or {"width": 1920, "height": 1080},
            )

            # 记录活跃上下文
            self._active_contexts[context_id] = {
                "context": context,
                "created_at": time.time(),
                "operation_type": operation_type,
                "priority": priority,
                "semaphore": semaphore
            }

            logger.info(f"成功创建上下文 {context_id}: type={operation_type}")
            return context

        except Exception as e:
            # 创建失败时释放信号量
            semaphore.release()
            logger.error(f"创建上下文失败 {context_id}: {e}")
            raise

    async def close_context(self, context: Optional[BrowserContext]):
        """关闭浏览器上下文并释放资源"""
        if not context:
            return

        # 查找对应的上下文记录
        context_id = None
        context_info = None
        for cid, info in self._active_contexts.items():
            if info["context"] == context:
                context_id = cid
                context_info = info
                break

        try:
            await context.close()
            logger.info(f"成功关闭上下文 {context_id or 'unknown'}")
        except Exception as e:
            logger.warning(f"关闭上下文出错 {context_id or 'unknown'}: {e}")
        finally:
            # 释放对应的信号量
            if context_info:
                context_info["semaphore"].release()
                # 从活跃上下文中移除
                if context_id:
                    del self._active_contexts[context_id]
                logger.info(f"释放上下文资源 {context_id}: type={context_info.get('operation_type', 'unknown')}")
            else:
                # 兜底：如果找不到记录，释放普通信号量
                self._semaphore.release()
                logger.warning("未找到上下文记录，释放普通信号量")

    async def open_page(self, context: BrowserContext) -> Page:
        page = await context.new_page()
        # 可注入通用监听器
        page.on("close", lambda: logger.info("页面已关闭"))
        page.on("crash", lambda: logger.warning("页面崩溃"))
        return page

    async def _cleanup_login_qrcode_contexts(self, new_operation_type: str):
        """清理同类型的登录二维码上下文

        Args:
            new_operation_type: 新的操作类型，用于确定要清理的上下文类型
        """
        # 提取平台类型（如 wechat_mp_login_qrcode -> wechat_mp）
        platform = new_operation_type.replace("_login_qrcode", "")

        contexts_to_cleanup = []
        for context_id, info in self._active_contexts.items():
            # 检查是否为同平台的登录二维码上下文
            if ("login_qrcode" in info["operation_type"] and
                info["operation_type"].startswith(platform)):
                contexts_to_cleanup.append((context_id, info))

        if contexts_to_cleanup:
            logger.info(f"发现 {len(contexts_to_cleanup)} 个同类型登录二维码上下文，准备清理")

            for context_id, info in contexts_to_cleanup:
                try:
                    logger.info(f"清理旧的登录二维码上下文 {context_id}: type={info['operation_type']}")
                    await info["context"].close()

                    # 释放信号量
                    info["semaphore"].release()

                    # 从活跃上下文中移除
                    del self._active_contexts[context_id]

                    logger.info(f"成功清理上下文 {context_id}")

                except Exception as e:
                    logger.error(f"清理上下文 {context_id} 时出错: {e}")
                    # 即使清理失败，也要释放信号量和移除记录
                    try:
                        info["semaphore"].release()
                        if context_id in self._active_contexts:
                            del self._active_contexts[context_id]
                    except Exception as cleanup_error:
                        logger.error(f"强制清理上下文 {context_id} 记录时出错: {cleanup_error}")

    def _log_active_contexts(self):
        """记录当前活跃的上下文信息"""
        if not self._active_contexts:
            logger.info("当前没有活跃的浏览器上下文")
            return

        logger.info(f"当前活跃上下文数量: {len(self._active_contexts)}")
        for context_id, info in self._active_contexts.items():
            age = time.time() - info["created_at"]
            logger.info(f"  {context_id}: type={info['operation_type']}, "
                       f"priority={info['priority']}, age={age:.1f}s")

    async def cleanup_expired_contexts(self, max_age_seconds: int = 600):
        """清理过期的上下文（默认10分钟）

        Args:
            max_age_seconds: 上下文最大存活时间（秒）
        """
        current_time = time.time()
        expired_contexts = []

        for context_id, info in self._active_contexts.items():
            age = current_time - info["created_at"]
            if age > max_age_seconds:
                expired_contexts.append((context_id, info, age))

        if expired_contexts:
            logger.info(f"发现 {len(expired_contexts)} 个过期上下文，准备清理")

            for context_id, info, age in expired_contexts:
                try:
                    logger.info(f"清理过期上下文 {context_id}: type={info['operation_type']}, age={age:.1f}s")
                    await info["context"].close()

                    # 释放信号量
                    info["semaphore"].release()

                    # 从活跃上下文中移除
                    del self._active_contexts[context_id]

                    logger.info(f"成功清理过期上下文 {context_id}")

                except Exception as e:
                    logger.error(f"清理过期上下文 {context_id} 时出错: {e}")
                    # 即使清理失败，也要释放信号量和移除记录
                    try:
                        info["semaphore"].release()
                        if context_id in self._active_contexts:
                            del self._active_contexts[context_id]
                    except Exception as cleanup_error:
                        logger.error(f"强制清理过期上下文 {context_id} 记录时出错: {cleanup_error}")

        return len(expired_contexts)

    def get_resource_status(self) -> Dict[str, Any]:
        """获取资源使用状态"""
        return {
            "total_contexts": len(self._active_contexts),
            "normal_available": self._semaphore._value,
            "priority_available": self._priority_semaphore._value,
            "active_contexts": [
                {
                    "id": context_id,
                    "operation_type": info["operation_type"],
                    "priority": info["priority"],
                    "age_seconds": time.time() - info["created_at"]
                }
                for context_id, info in self._active_contexts.items()
            ]
        }

    async def cleanup_stale_contexts(self, max_age_seconds: float = 3600):
        """清理长时间未使用的上下文"""
        current_time = time.time()
        stale_contexts = []

        for context_id, info in self._active_contexts.items():
            if current_time - info["created_at"] > max_age_seconds:
                stale_contexts.append((context_id, info))

        for context_id, info in stale_contexts:
            logger.warning(f"清理过期上下文 {context_id}: age={current_time - info['created_at']:.1f}s")
            try:
                await info["context"].close()
            except Exception as e:
                logger.error(f"清理过期上下文失败 {context_id}: {e}")
            finally:
                info["semaphore"].release()
                del self._active_contexts[context_id]


# 全局单例
browser_manager = BrowserManager()

