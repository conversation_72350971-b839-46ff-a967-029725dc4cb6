import asyncio
import time

import base64
import json
import re
import os
from datetime import datetime, timed<PERSON>ta
from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext
from app.services.browser_manager import browser_manager
from typing import Optional, Dict, Any, List, Tuple
from app.services.platform_service_base import PlatformServiceBase, DataDownloadResult

class WeChatMPService(PlatformServiceBase):
    # 数据下载模板配置
    DOWNLOAD_TEMPLATES = {
        'content_trend': {
            'name': '内容数据趋势明细表',
            'url_type': 'datacubequery',
            'busi': 3,
            'tmpl': 14,
            'date_format': 'number',  # 数字格式：20250619
            'args_template': '{{"begin_date":{begin_date},"end_date":{end_date}}}',
            'data_start_row': 2,  # 数据从第2行开始（跳过标题行）
            'fields': [
                ('日期', 1),  # 多行文本
                ('阅读次数', 2),  # 数字
                ('阅读人数', 2),  # 数字
                ('分享次数', 2),  # 数字
                ('分享人数', 2),  # 数字
                ('阅读原文次数', 2),  # 数字
                ('阅读原文人数', 2),  # 数字
                ('收藏次数', 2),  # 数字
                ('收藏人数', 2),  # 数字
                ('群发篇数', 2),  # 数字
                ('渠道', 1),  # 多行文本
            ]
        },
        'content_source': {
            'name': '内容流量来源明细表',
            'url_type': 'datacubequery',
            'busi': 5,
            'tmpl': 16,
            'date_format': 'string',  # 字符串格式："20250619"
            'args_template': '{{"beg_date":"{begin_date}","end_date":"{end_date}"}}',
            'data_start_row': 2,  # 数据从第2行开始（跳过标题行）
            'fields': [
                ('传播渠道', 1),  # 多行文本
                ('发表日期', 1),  # 多行文本
                ('内容标题', 1),  # 多行文本
                ('阅读次数', 2),  # 数字
                ('阅读次数占比', 1),  # 多行文本（百分比格式）
            ]
        },
        'content_detail': {
            'name': '内容已通知内容明细表',
            'url_type': 'datacubequery',
            'busi': 3,
            'tmpl': 19,
            'date_format': 'number',  # 数字格式：20250619
            'args_template': '{{"begin_date":{begin_date},"end_date":{end_date}}}',
            'data_start_row': 2,  # 数据从第2行开始（跳过标题行）
            'fields': [
                ('内容标题', 1),  # 多行文本
                ('发表时间', 1),  # 多行文本
                ('总阅读人数', 2),  # 数字
                ('总阅读次数', 2),  # 数字
                ('总分享人数', 2),  # 数字
                ('总分享次数', 2),  # 数字
                ('阅读后关注人数', 2),  # 数字
                ('送达人数', 2),  # 数字
                ('公众号消息阅读次数', 2),  # 数字
                ('送达阅读率', 1),  # 多行文本（百分比格式）
                ('首次分享次数', 2),  # 数字
                ('分享产生阅读次数', 2),  # 数字
                ('首次分享率', 1),  # 多行文本（百分比格式）
                ('每次分享带来阅读次数', 2),  # 数字
                ('阅读完成率', 1),  # 多行文本（百分比格式）
                ('内容url', 15),  # 超链接
            ]
        },
        'user_channel': {
            'name': '用户增长表',
            'url_type': 'useranalysis',
            'date_format': 'dash',  # 带分隔符格式：2025-06-19
            'url_template': 'https://mp.weixin.qq.com/misc/useranalysis?=&download=1&begin_date={begin_date}&end_date={end_date}&source=99999999&token={token}&lang=zh_CN',
            'data_start_row': 4,  # 数据从第2行开始（跳过标题行）
            'fields': [
                ('时间', 1),  # 多行文本
                ('新关注人数', 2),  # 数字
                ('取消关注人数', 2),  # 数字
                ('净增关注人数', 2),  # 数字
                ('累积关注人数', 2),  # 数字
            ]
        },
        'user_source': {
            'name': '用户来源数据',
            'url_type': 'ajax',  # 特殊类型，通过AJAX获取
            'date_format': 'dash',  # 带分隔符格式：2025-06-19
            'data_start_row': 1,  # 数据从第1行开始
            'fields': [
                ('用户来源', 1),  # 多行文本
                ('日期', 1),  # 多行文本
                ('新增用户', 2),  # 数字
                ('取消用户', 2),  # 数字
                ('净增用户', 2),  # 数字
                ('累计用户', 2),  # 数字
            ]
        }
    }

    def __init__(self, account_id: Optional[int] = None, headless: bool = True):
        super().__init__(account_id)
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.playwright = None
        self.headless = headless  # 添加headless参数
        self.user_data_dir = self._get_user_data_dir()
        # 登录二维码缓存（减少重复创建上下文 & 支持重复点击立即返回）
        self._last_qr_base64: Optional[str] = None
        self._last_qr_timestamp: Optional[float] = None


    def _get_user_data_dir(self) -> str:
        """获取用户数据目录路径"""
        if self.account_id:
            # 为每个账号创建独立的用户数据目录
            base_dir = os.path.join(os.getcwd(), "user_data")
            user_dir = os.path.join(base_dir, f"wechat_account_{self.account_id}")
        else:
            # 默认目录
            user_dir = os.path.join(os.getcwd(), "user_data", "default")

        # 确保目录存在
        os.makedirs(user_dir, exist_ok=True)
        return user_dir

    async def _create_persistent_context(self) -> BrowserContext:
        """创建持久化的浏览器上下文（改为通过全局 BrowserManager）"""
        # 使用全局管理器创建上下文
        self.context = await browser_manager.create_context(
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            viewport={"width": 1920, "height": 1080},
            accept_downloads=True,
            operation_type="wechat_mp_context"
        )
        return self.context

    async def save_login_state(self, file_path: Optional[str] = None) -> bool:
        """保存登录状态到文件"""
        try:
            if not self.context:
                print("没有可用的浏览器上下文")
                return False

            if not file_path:
                file_path = os.path.join(self.user_data_dir, "login_state.json")

            # 获取cookies
            cookies = await self.context.cookies()

            # 获取localStorage
            storage_state = await self.context.storage_state()

            # 保存状态
            login_state = {
                "cookies": cookies,
                "storage_state": storage_state,
                "saved_at": datetime.now().isoformat(),
                "account_id": self.account_id
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(login_state, f, ensure_ascii=False, indent=2)

            print(f"登录状态已保存到: {file_path}")
            return True

        except Exception as e:
            print(f"保存登录状态失败: {e}")
            return False

    async def load_login_state(self, file_path: Optional[str] = None) -> bool:
        """从文件加载登录状态"""
        try:
            if not file_path:
                file_path = os.path.join(self.user_data_dir, "login_state.json")

            if not os.path.exists(file_path):
                print(f"登录状态文件不存在: {file_path}")
                return False

            with open(file_path, 'r', encoding='utf-8') as f:
                login_state = json.load(f)

            # 检查状态是否过期（比如7天）
            saved_at = datetime.fromisoformat(login_state.get("saved_at", ""))
            if datetime.now() - saved_at > timedelta(days=7):
                print("登录状态已过期")
                os.remove(file_path)  # 删除过期的状态文件
                return False

            # 兼容保留：已改为使用全局 BrowserManager，无需自管浏览器

            # 使用保存的状态创建上下文（仅当尚未存在时，避免重复占用信号量）
            if not self.context:
                storage_state = login_state.get("storage_state")
                if storage_state:
                    self.context = await browser_manager.create_context(
                        user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                        viewport={"width": 1920, "height": 1080},
                        storage_state=storage_state,
                        accept_downloads=True,
                    )
                else:
                    # 只恢复cookies的情况
                    self.context = await self._create_persistent_context()
                    # 添加cookies
                    cookies = login_state.get("cookies", [])
                    if cookies:
                        await self.context.add_cookies(cookies)

            # 创建页面（仅在不存在时创建）
            if not self.page:
                self.page = await browser_manager.open_page(self.context)

            print(f"登录状态已从 {file_path} 恢复")
            return True

        except Exception as e:
            print(f"加载登录状态失败: {e}")
            return False

    async def _init_browser(self):
        """保持兼容的空实现：已改为使用全局 BrowserManager"""
        return

    async def get_login_qrcode(self) -> Optional[str]:
        """获取微信公众号登录二维码"""
        try:
            # 首先尝试加载已保存的登录状态
            if await self.load_login_state():
                # 检查是否已经登录
                if await self.check_existing_login():
                    print("检测到已有有效的登录状态")
                    await self._update_database_login_status(True)
                    return "already_logged_in"

            # 若最近30秒内已有二维码缓存，直接返回
            if getattr(self, "_last_qr_base64", None) and getattr(self, "_last_qr_timestamp", None):
                if time.time() - self._last_qr_timestamp < 30:
                    print("返回最近缓存的公众号二维码（30秒内）")
                    return f"data:image/png;base64,{self._last_qr_base64}"

            # 如果没有有效的登录状态，清理并重新开始
            await self.close()

            # 使用全局浏览器管理器创建上下文与页面（高优先级）
            self.context = await browser_manager.create_context(
                user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                viewport={"width": 1920, "height": 1080},
                accept_downloads=True,
                priority=True,  # 用户交互操作使用高优先级
                timeout=30.0,   # 30秒超时
                operation_type="wechat_mp_login_qrcode"
            )
            self.page = await browser_manager.open_page(self.context)

            print("正在访问微信公众号登录页面...")
            # 访问微信公众号登录页面，增加超时时间
            await self.page.goto("https://mp.weixin.qq.com", wait_until="domcontentloaded", timeout=60000)

            # 等待页面完全加载
            # await asyncio.sleep(2)

            # 尝试多个可能的二维码选择器
            qr_selectors = [
                ".login__type__container__scan__qrcode",
                ".qrcode",
                ".login_qrcode",
                ".weui-desktop-login__qrcode",
                "img[src*='qr']",
                ".login-qrcode img"
            ]

            qr_element = None
            for selector in qr_selectors:
                try:
                    print(f"尝试选择器: {selector}")
                    await self.page.wait_for_selector(selector, state="visible", timeout=10000)
                    qr_element = await self.page.query_selector(selector)
                    if qr_element:
                        print(f"找到二维码元素: {selector}")
                        break
                except Exception as e:
                    print(f"选择器 {selector} 失败: {e}")
                    continue

            if not qr_element:
                # 如果找不到二维码，尝试截取整个页面查看问题
                page_screenshot = await self.page.screenshot()
                page_base64 = base64.b64encode(page_screenshot).decode()
                print("未找到二维码元素，返回整个页面截图用于调试")
                return f"data:image/png;base64,{page_base64}"

            # 等待二维码图片完全加载
            await asyncio.sleep(1)

            # 检查元素是否可见
            is_visible = await qr_element.is_visible()
            if not is_visible:
                print("二维码元素不可见")
                return None

            # 获取二维码图片
            qr_screenshot = await qr_element.screenshot()

            # 检查截图是否为空
            if len(qr_screenshot) == 0:
                print("二维码截图为空")
                return None

            # 转换为base64并缓存
            qr_base64 = base64.b64encode(qr_screenshot).decode()
            self._last_qr_base64 = qr_base64
            self._last_qr_timestamp = time.time()

            print(f"成功获取二维码，大小: {len(qr_screenshot)} bytes")
            return f"data:image/png;base64,{qr_base64}"


        except Exception as e:
            print(f"获取二维码失败: {e}")
            # 在出错时也尝试清理资源
            await self.close()
            return None

    async def check_existing_login(self) -> bool:
        """检查现有登录状态是否仍然有效"""
        try:
            if not self.page:
                return False

            # 访问微信公众号首页
            await self.page.goto("https://mp.weixin.qq.com", wait_until="domcontentloaded", timeout=15000)
            await asyncio.sleep(2)

            # 检查是否已经登录（URL包含token）
            current_url = self.page.url
            if "token=" in current_url:
                print("检测到有效的登录状态")
                return True
            else:
                print("登录状态已失效")
                return False

        except Exception as e:
            print(f"检查现有登录状态失败: {e}")
            return False

    async def get_cached_or_rescreenshot_qrcode(self) -> Optional[str]:
        """返回最近缓存的二维码；若无缓存且页面仍在，则在当前页面重新截图，避免重复创建上下文。"""
        # 先返回非过期缓存（30 秒）
        if getattr(self, "_last_qr_base64", None) and getattr(self, "_last_qr_timestamp", None):
            if time.time() - self._last_qr_timestamp < 30:
                return f"data:image/png;base64,{self._last_qr_base64}"

        # 尝试在现有页面重新截图
        if self.page:
            try:
                await self.page.wait_for_load_state("domcontentloaded", timeout=15000)
                selectors = [
                    ".login__type__container__scan__qrcode",
                    ".qrcode",
                    ".login_qrcode",
                    ".weui-desktop-login__qrcode",
                    "img[src*='qr']",
                    ".login-qrcode img",
                ]
                el = None
                for sel in selectors:
                    try:
                        el = await self.page.wait_for_selector(sel, timeout=2000)
                        if el:
                            break
                    except Exception:
                        continue
                if not el:
                    return None
                img_bytes = await el.screenshot()
                if not img_bytes:
                    return None
                b64 = base64.b64encode(img_bytes).decode()
                self._last_qr_base64 = b64
                self._last_qr_timestamp = time.time()
                return f"data:image/png;base64,{b64}"
            except Exception:
                return None

        return None





    async def _update_database_login_status(self, is_logged_in: bool):
        """更新数据库中的登录状态"""
        try:
            from app.database import SessionLocal
            from app.models import PlatformAccount
            from datetime import datetime

            db = SessionLocal()
            try:
                account = db.query(PlatformAccount).filter(PlatformAccount.id == self.account_id).first()
                if account:
                    account.login_status = is_logged_in
                    if is_logged_in:
                        account.last_login_time = datetime.now()
                        # 获取并保存cookies
                        try:
                            cookies = await self.get_cookies()
                            account.cookies = cookies
                        except Exception as e:
                            print(f"获取cookies失败: {e}")
                    else:
                        account.cookies = None

                    db.commit()
                    print(f"已更新账号 {self.account_id} 的数据库登录状态: {is_logged_in}")
                else:
                    print(f"未找到账号 {self.account_id}")
            finally:
                db.close()

        except Exception as e:
            print(f"更新数据库登录状态失败: {e}")

    async def logout(self, clear_saved_state: bool = True) -> bool:
        """注销登录

        Args:
            clear_saved_state: 是否清除保存的登录状态文件

        Returns:
            是否成功注销
        """
        try:
            print("开始注销登录...")

            # 1. 尝试从微信公众号后台注销
            logout_success = await self._logout_from_wechat()

            # 2. 清除浏览器会话
            await self._clear_browser_session()

            # 3. 清除保存的登录状态文件
            if clear_saved_state:
                self._clear_saved_login_state()

            print("✅ 注销登录完成")
            return logout_success

        except Exception as e:
            print(f"注销登录过程中发生错误: {e}")
            # 即使出错也要清理本地状态
            try:
                await self._clear_browser_session()
                if clear_saved_state:
                    self._clear_saved_login_state()
            except:
                pass
            return False

    async def _logout_from_wechat(self) -> bool:
        """从微信公众号后台注销"""
        try:
            if not self.page:
                print("页面未初始化，跳过微信后台注销")
                return True

            # 检查当前是否已登录
            current_url = self.page.url
            if "token=" not in current_url:
                print("当前未登录，无需从微信后台注销")
                return True

            print("尝试从微信公众号后台注销...")

            # 提取当前token
            token = self._extract_token_from_url()

            # 方法1: 尝试访问正确的注销URL（基于你观察到的实际URL）
            try:
                if token:
                    # 使用正确的注销URL格式
                    logout_url = f"https://mp.weixin.qq.com/cgi-bin/logout?t=wxm-logout&token={token}&lang=zh_CN"
                    print(f"使用注销URL: {logout_url}")
                else:
                    # 如果无法提取token，使用通用的注销URL
                    logout_url = "https://mp.weixin.qq.com/cgi-bin/logout?t=wxm-logout&lang=zh_CN"
                    print(f"使用通用注销URL: {logout_url}")

                await self.page.goto(logout_url, wait_until="domcontentloaded", timeout=10000)
                await asyncio.sleep(2)

                # 检查是否已经注销（URL不再包含token或跳转到登录页）
                new_url = self.page.url
                if "token=" not in new_url or "mp.weixin.qq.com" in new_url and new_url.find("/") == new_url.rfind("/"):
                    print("✅ 通过注销URL成功注销")
                    return True

            except Exception as e:
                print(f"注销URL方法失败: {e}")

            # 方法2: 尝试点击页面上的注销按钮/链接
            try:
                # 常见的注销元素选择器（基于实际观察更新）
                logout_selectors = [
                    "a[href*='cgi-bin/logout']",  # 基于你观察到的URL格式
                    "a[href*='loginquit']",       # 原有的格式
                    "a[href*='logout']",
                    ".logout",
                    ".quit",
                    "[data-action='logout']",
                    "a:contains('退出')",
                    "a:contains('注销')",
                    ".js_quit",
                    ".header_tool_logout",        # 可能的页面头部注销按钮
                    ".tool_logout"                # 可能的工具栏注销按钮
                ]

                for selector in logout_selectors:
                    try:
                        logout_element = await self.page.query_selector(selector)
                        if logout_element and await logout_element.is_visible():
                            print(f"找到注销元素: {selector}")
                            await logout_element.click()
                            await asyncio.sleep(3)

                            # 检查是否成功注销
                            new_url = self.page.url
                            if "token=" not in new_url:
                                print("✅ 通过点击注销元素成功注销")
                                return True
                            break
                    except Exception as inner_e:
                        continue

            except Exception as e:
                print(f"点击注销元素方法失败: {e}")

            # 方法3: 清除所有cookies（强制注销）
            try:
                print("尝试通过清除cookies强制注销...")
                if self.context:
                    # 清除所有cookies
                    await self.context.clear_cookies()

                    # 刷新页面验证
                    await self.page.reload(wait_until="domcontentloaded", timeout=10000)
                    await asyncio.sleep(2)

                    new_url = self.page.url
                    if "token=" not in new_url:
                        print("✅ 通过清除cookies成功注销")
                        return True

            except Exception as e:
                print(f"清除cookies方法失败: {e}")

            print("⚠️ 微信后台注销可能未完全成功，但将继续清理本地状态")
            return False

        except Exception as e:
            print(f"从微信后台注销失败: {e}")
            return False

    async def _clear_browser_session(self):
        """清除浏览器会话"""
        try:
            print("清除浏览器会话...")

            # 清除当前上下文的所有数据
            if self.context:
                try:
                    # 清除cookies
                    await self.context.clear_cookies()

                    # 清除localStorage和sessionStorage
                    if self.page:
                        await self.page.evaluate("""
                            () => {
                                try {
                                    localStorage.clear();
                                    sessionStorage.clear();
                                } catch (e) {
                                    console.log('清除存储失败:', e);
                                }
                            }
                        """)

                    print("✅ 浏览器会话数据已清除")

                except Exception as e:
                    print(f"清除浏览器会话数据失败: {e}")

            # 关闭当前页面和上下文
            await self.close()

        except Exception as e:
            print(f"清除浏览器会话失败: {e}")

    def _clear_saved_login_state(self):
        """清除保存的登录状态文件"""
        try:
            state_file = os.path.join(self.user_data_dir, "login_state.json")

            if os.path.exists(state_file):
                os.remove(state_file)
                print(f"✅ 已删除保存的登录状态文件: {state_file}")
            else:
                print("没有找到需要删除的登录状态文件")

            # 如果整个账号目录为空，也删除目录
            try:
                if os.path.exists(self.user_data_dir) and not os.listdir(self.user_data_dir):
                    os.rmdir(self.user_data_dir)
                    print(f"✅ 已删除空的用户数据目录: {self.user_data_dir}")
            except:
                pass  # 目录不为空或其他原因，忽略

        except Exception as e:
            print(f"清除保存的登录状态文件失败: {e}")

    async def force_logout(self) -> bool:
        """强制注销（忽略所有错误，确保本地状态被清理）"""
        try:
            print("开始强制注销...")

            # 直接清理所有状态，不管是否成功
            tasks = []

            # 异步执行清理任务
            if self.context:
                tasks.append(self._clear_browser_session())

            # 等待所有清理任务完成（有超时保护）
            if tasks:
                try:
                    await asyncio.wait_for(
                        asyncio.gather(*tasks, return_exceptions=True),
                        timeout=10.0
                    )
                except asyncio.TimeoutError:
                    print("⚠️ 清理任务超时，强制继续")

            # 清除保存的状态文件
            self._clear_saved_login_state()

            # 重置所有实例变量
            self.page = None
            self.context = None
            self.browser = None
            self.playwright = None

            print("✅ 强制注销完成")
            return True

        except Exception as e:
            print(f"强制注销过程中发生错误: {e}")
            # 即使出错也要尝试清理基本状态
            try:
                self._clear_saved_login_state()
                self.page = None
                self.context = None
                self.browser = None
                self.playwright = None
            except:
                pass
            return True  # 强制注销总是返回成功

    async def refresh_qrcode(self) -> Optional[str]:
        """刷新二维码（当二维码过期时使用）"""
        try:
            if not self.page:
                print("页面未初始化，重新获取二维码")
                return await self.get_login_qrcode()

            print("正在刷新页面并重新获取二维码...")
            # 刷新页面
            await self.page.reload(wait_until="domcontentloaded", timeout=30000)
            await asyncio.sleep(2)

            # 清除缓存的二维码，强制重新获取
            self._last_qr_base64 = None
            self._last_qr_timestamp = None

            # 尝试多个可能的二维码选择器
            qr_selectors = [
                ".login__type__container__scan__qrcode",
                ".qrcode",
                ".login_qrcode",
                ".weui-desktop-login__qrcode",
                "img[src*='qr']",
                ".login-qrcode img"
            ]

            qr_element = None
            for selector in qr_selectors:
                try:
                    await self.page.wait_for_selector(selector, state="visible", timeout=10000)
                    qr_element = await self.page.query_selector(selector)
                    if qr_element:
                        print(f"刷新后找到二维码元素: {selector}")
                        break
                except Exception as e:
                    continue

            if not qr_element:
                print("刷新后仍未找到二维码元素")
                return None

            # 等待二维码图片完全加载
            await asyncio.sleep(1)

            # 获取二维码图片
            qr_screenshot = await qr_element.screenshot()

            if len(qr_screenshot) == 0:
                print("刷新后二维码截图为空")
                return None

            # 转换为base64并缓存
            qr_base64 = base64.b64encode(qr_screenshot).decode()
            self._last_qr_base64 = qr_base64
            self._last_qr_timestamp = time.time()

            print(f"成功刷新二维码，大小: {len(qr_screenshot)} bytes")
            return f"data:image/png;base64,{qr_base64}"

        except Exception as e:
            print(f"刷新二维码失败: {e}")
            return None

    async def check_login_status(self, wait_for_redirect: bool = True, timeout: int = 30) -> bool:
        """检查登录状态"""
        try:
            if not self.page:
                print("页面未初始化")
                return False

            # 获取当前URL
            current_url = self.page.url
            print(f"当前URL: {current_url}")

            # 如果已经有token，直接返回登录成功
            if "mp.weixin.qq.com" in current_url and "token=" in current_url:
                print("检测到已登录状态")
                return True

            # 检查是否还在登录页面
            if "mp.weixin.qq.com" in current_url and "token=" not in current_url:
                # 检查是否已经扫码成功但页面还没跳转
                success_indicators = [
                    # 扫码成功的提示文字或元素
                    ".login__type__container__scan__desc--success",
                    ".scan-success",
                    ".login-success",
                    "[class*='success']",
                    # 可能的跳转提示
                    ".redirecting",
                    ".loading"
                ]

                login_success_detected = False
                for selector in success_indicators:
                    try:
                        success_element = await self.page.query_selector(selector)
                        if success_element and await success_element.is_visible():
                            print(f"检测到登录成功指示器: {selector}")
                            login_success_detected = True
                            break
                    except:
                        continue

                # 检查页面内容变化（比如二维码消失，出现用户头像等）
                if not login_success_detected:
                    try:
                        # 检查二维码是否消失或被替换
                        qr_element = await self.page.query_selector(".login__type__container__scan__qrcode")
                        if not qr_element or not await qr_element.is_visible():
                            print("二维码已消失，可能已扫码成功")
                            login_success_detected = True

                        # 检查是否出现用户头像或其他登录后的元素
                        avatar_selectors = [
                            ".user-avatar",
                            ".avatar",
                            ".user-info",
                            "[class*='avatar']",
                            "img[src*='avatar']"
                        ]

                        for selector in avatar_selectors:
                            try:
                                avatar_element = await self.page.query_selector(selector)
                                if avatar_element and await avatar_element.is_visible():
                                    print(f"检测到用户头像: {selector}")
                                    login_success_detected = True
                                    break
                            except:
                                continue

                    except Exception as inner_e:
                        print(f"检查页面元素变化失败: {inner_e}")

                # 如果检测到登录成功但还没跳转，等待页面跳转
                if login_success_detected and wait_for_redirect:
                    print(f"检测到登录成功，等待页面跳转（最多{timeout}秒）...")

                    try:
                        # 等待URL变化（包含token）或页面跳转
                        await self.page.wait_for_function(
                            "() => window.location.href.includes('token=')",
                            timeout=timeout * 1000
                        )

                        new_url = self.page.url
                        print(f"页面已跳转到: {new_url}")

                        if "token=" in new_url:
                            print("登录成功并完成跳转")
                            return True

                    except Exception as wait_e:
                        print(f"等待页面跳转超时: {wait_e}")

                        # 尝试手动刷新页面
                        print("尝试刷新页面触发跳转...")
                        try:
                            await self.page.reload(wait_until="domcontentloaded", timeout=10000)
                            await asyncio.sleep(2)

                            new_url = self.page.url
                            if "token=" in new_url:
                                print("刷新后检测到登录状态")
                                return True
                        except:
                            print("刷新页面失败")

                # 检查二维码是否过期
                try:
                    expired_selectors = [
                        ".login__type__container__scan__desc--fail",
                        ".qrcode-expired",
                        ".login-qrcode-expired",
                        "[class*='expired']",
                        "[class*='fail']"
                    ]

                    for selector in expired_selectors:
                        expired_element = await self.page.query_selector(selector)
                        if expired_element and await expired_element.is_visible():
                            print("检测到二维码已过期")
                            return False

                except Exception as inner_e:
                    print(f"检查二维码过期状态失败: {inner_e}")

                if login_success_detected:
                    print("检测到登录成功，但页面跳转可能延迟")
                    # 可能已经登录成功，但页面跳转有延迟
                    return True
                else:
                    print("仍在等待扫码登录")
                    return False

            print("页面状态异常")
            return False

        except Exception as e:
            print(f"检查登录状态失败: {e}")
            return False

    def _get_download_config(self, data_type: Optional[str] = None, busi: Optional[int] = 3, tmpl: Optional[int] = 19) -> Dict[str, Any]:
        """获取下载配置

        Args:
            data_type: 数据类型，如果指定则使用模板配置
            busi: 业务类型（向后兼容，可为None）
            tmpl: 模板类型（向后兼容，可为None）

        Returns:
            下载配置字典
        """
        if data_type and data_type in self.DOWNLOAD_TEMPLATES:
            return self.DOWNLOAD_TEMPLATES[data_type]
        elif busi is not None and tmpl is not None:
            # 向后兼容：根据busi和tmpl参数匹配对应的数据类型
            for dt, config in self.DOWNLOAD_TEMPLATES.items():
                if (config.get('busi') == busi and config.get('tmpl') == tmpl):
                    return config

            # 如果没有匹配到，使用默认配置（content_detail）
            return self.DOWNLOAD_TEMPLATES['content_detail']
        else:
            # 如果busi和tmpl都为None，使用默认配置
            return self.DOWNLOAD_TEMPLATES['content_detail']

    def _format_date_for_template(self, date_str: str, date_format: str) -> Optional[str]:
        """根据模板要求格式化日期

        Args:
            date_str: 输入日期字符串 (YYYY-MM-DD 或 YYYYMMDD)
            date_format: 目标格式 ('number', 'string', 'dash')

        Returns:
            格式化后的日期字符串
        """
        try:
            # 先统一解析为标准格式
            date_str = date_str.strip()

            # 如果已经是8位数字格式
            if len(date_str) == 8 and date_str.isdigit():
                base_date = date_str
            elif '-' in date_str:
                # YYYY-MM-DD格式
                parts = date_str.split('-')
                if len(parts) == 3:
                    year, month, day = parts
                    base_date = f"{year}{month.zfill(2)}{day.zfill(2)}"
                else:
                    return None
            elif '/' in date_str:
                # YYYY/MM/DD格式
                parts = date_str.split('/')
                if len(parts) == 3:
                    year, month, day = parts
                    base_date = f"{year}{month.zfill(2)}{day.zfill(2)}"
                else:
                    return None
            else:
                return None

            # 根据目标格式转换
            if date_format == 'number':
                return base_date  # 20250619
            elif date_format == 'string':
                return base_date  # "20250619" (在URL中会被引号包围)
            elif date_format == 'dash':
                # 转换为YYYY-MM-DD格式
                if len(base_date) == 8:
                    return f"{base_date[:4]}-{base_date[4:6]}-{base_date[6:8]}"
                else:
                    return None
            else:
                return base_date

        except Exception as e:
            print(f"日期格式化失败: {e}")
            return None

    def _build_download_url(self, config: Dict[str, Any], begin_date: str, end_date: str, token: str) -> str:
        """构建下载URL

        Args:
            config: 下载配置
            begin_date: 开始日期
            end_date: 结束日期
            token: 访问令牌

        Returns:
            完整的下载URL
        """
        url_type = config.get('url_type', 'datacubequery')

        if url_type == 'useranalysis':
            # 用户分析类型，使用自定义URL模板
            url_template = config.get('url_template', '')
            return url_template.format(
                begin_date=begin_date,
                end_date=end_date,
                token=token
            )
        else:
            # datacubequery类型，使用标准格式
            busi = config.get('busi', 3)
            tmpl = config.get('tmpl', 19)
            args_template = config.get('args_template', '{{"begin_date":{begin_date},"end_date":{end_date}}}')

            args = args_template.format(
                begin_date=begin_date,
                end_date=end_date
            )

            return f"https://mp.weixin.qq.com/misc/datacubequery?action=query_download&busi={busi}&tmpl={tmpl}&args={args}&token={token}&lang=zh_CN"

    async def wait_for_login_complete(self, max_wait_time: int = 300) -> bool:
        """等待用户完成扫码登录，返回是否登录成功"""
        try:
            if not self.page:
                print("页面未初始化")
                return False

            print(f"开始等待用户扫码登录（最多等待{max_wait_time}秒）...")

            start_time = asyncio.get_event_loop().time()
            check_interval = 2  # 每2秒检查一次

            while True:
                current_time = asyncio.get_event_loop().time()
                elapsed_time = current_time - start_time

                if elapsed_time > max_wait_time:
                    print("等待登录超时")
                    return False

                # 检查登录状态
                login_status = await self.check_login_status(wait_for_redirect=True, timeout=10)

                if login_status:
                    print("用户已成功登录")
                    # 保存登录状态
                    await self.save_login_state()
                    return True

                # 检查二维码是否过期，如果过期则自动刷新
                try:
                    expired_selectors = [
                        ".login__type__container__scan__desc--fail",
                        ".qrcode-expired",
                        ".login-qrcode-expired",
                        "[class*='expired']",
                        "[class*='fail']"
                    ]

                    qr_expired = False
                    for selector in expired_selectors:
                        expired_element = await self.page.query_selector(selector)
                        if expired_element and await expired_element.is_visible():
                            print("检测到二维码已过期，自动刷新...")
                            qr_expired = True
                            break

                    if qr_expired:
                        # 刷新二维码
                        new_qr = await self.refresh_qrcode()
                        if new_qr:
                            print("二维码已刷新，请重新扫码")
                        else:
                            print("刷新二维码失败")
                            return False

                except Exception as check_e:
                    print(f"检查二维码状态失败: {check_e}")

                # 显示剩余时间
                remaining_time = max_wait_time - elapsed_time
                print(f"等待扫码中... 剩余时间: {remaining_time:.0f}秒")

                await asyncio.sleep(check_interval)

        except Exception as e:
            print(f"等待登录过程中发生错误: {e}")
            return False

    async def download_data_excel(self, begin_date: str, end_date: str, busi: int = 3, tmpl: int = 19, data_type: Optional[str] = None, auto_import: bool = True) -> Optional[bytes]:
        """下载微信公众号数据Excel文件

        Args:
            begin_date: 开始日期，格式: YYYYMMDD 或 YYYY-MM-DD
            end_date: 结束日期，格式: YYYYMMDD 或 YYYY-MM-DD
            busi: 业务类型，默认3（向后兼容）
            tmpl: 模板类型，默认19（向后兼容）
            data_type: 数据类型，可选值: content_trend, content_source, content_detail, user_channel
            auto_import: 是否自动导入数据到数据库，默认True

        Returns:
            Excel文件的二进制数据，失败返回None
        """
        try:
            if not self.page:
                print("页面未初始化，无法下载数据")
                return None

            # 检查当前URL，如果不在微信公众号页面，先导航过去
            current_url = self.page.url
            print(f"当前URL: {current_url}")

            if "mp.weixin.qq.com" not in current_url or "token=" not in current_url:
                print("当前不在微信公众号页面，正在导航到微信公众号主页...")
                # 导航到微信公众号主页，会自动跳转到登录后的页面
                try:
                    await self.page.goto("https://mp.weixin.qq.com",
                                       wait_until="domcontentloaded", timeout=15000)
                    await asyncio.sleep(3)  # 等待自动跳转完成

                    # 更新当前URL
                    current_url = self.page.url
                    print(f"导航后的URL: {current_url}")

                    # 再次检查是否有token
                    if "token=" not in current_url:
                        print("导航后仍未检测到登录状态，可能登录已过期")
                        return None

                except Exception as nav_e:
                    print(f"导航到微信公众号页面失败: {nav_e}")
                    return None

            # 提取token
            token = self._extract_token_from_url()
            if not token:
                print("无法获取token，无法下载数据")
                return None

            # 获取下载配置
            config = self._get_download_config(data_type, busi, tmpl)
            print(f"使用配置: {config.get('name', '未知类型')}")

            # 根据配置格式化日期
            date_format = config.get('date_format', 'number')
            formatted_begin_date = self._format_date_for_template(begin_date, date_format)
            formatted_end_date = self._format_date_for_template(end_date, date_format)

            if not formatted_begin_date or not formatted_end_date:
                print("日期格式错误")
                return None

            print(f"开始下载数据: {formatted_begin_date} 到 {formatted_end_date}")

            # 构建下载URL
            download_url = self._build_download_url(config, formatted_begin_date, formatted_end_date, token)

            print(f"下载URL: {download_url}")

            # 直接使用HTTP请求下载（原备用方法改为首选）
            try:
                # 获取当前页面的cookies
                cookies = await self.context.cookies()

                # 构建cookie字符串
                cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])

                # 使用页面的request方法发送请求
                response = await self.page.request.get(
                    download_url,
                    headers={
                        'Cookie': cookie_str,
                        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                        'Referer': 'https://mp.weixin.qq.com/'
                    }
                )

                if response.status == 200:
                    file_content = await response.body()
                    print(f"数据下载成功，文件大小: {len(file_content)} bytes")

                    # 自动导入数据到数据库
                    if auto_import and self.account_id and data_type:
                        await self._import_excel_to_database(file_content, data_type)

                    return file_content
                else:
                    print(f"下载失败，状态码: {response.status}")
                    # 尝试获取错误信息
                    error_text = await response.text()
                    print(f"错误响应: {error_text}")
                    return None

            except Exception as request_e:
                print(f"HTTP请求下载失败: {request_e}")
                # 如果HTTP请求失败，尝试页面下载方法
                return await self._download_data_fallback_page_method(config, formatted_begin_date, formatted_end_date, token, data_type, auto_import)

        except Exception as e:
            print(f"下载数据失败: {e}")
            return None

    async def download_data_excel_only(self, begin_date: str, end_date: str, data_type: str) -> Optional[bytes]:
        """仅下载微信公众号数据Excel文件，不导入数据库

        Args:
            begin_date: 开始日期，格式: YYYYMMDD 或 YYYY-MM-DD
            end_date: 结束日期，格式: YYYYMMDD 或 YYYY-MM-DD
            data_type: 数据类型，可选值: content_trend, content_source, content_detail, user_channel

        Returns:
            Excel文件的二进制数据，失败返回None
        """
        try:
            if not self.page:
                print("页面未初始化，无法下载数据")
                return None

            # 检查当前URL，如果不在微信公众号页面，先导航过去
            current_url = self.page.url
            print(f"当前URL: {current_url}")

            if "mp.weixin.qq.com" not in current_url or "token=" not in current_url:
                print("当前不在微信公众号页面，正在导航到微信公众号主页...")
                try:
                    await self.page.goto("https://mp.weixin.qq.com",
                                       wait_until="domcontentloaded", timeout=15000)
                    await asyncio.sleep(3)

                    current_url = self.page.url
                    print(f"导航后的URL: {current_url}")

                    if "token=" not in current_url:
                        print("导航后仍未检测到登录状态，可能登录已过期")
                        return None

                except Exception as nav_e:
                    print(f"导航到微信公众号页面失败: {nav_e}")
                    return None

            # 提取token
            token = self._extract_token_from_url()
            if not token:
                print("无法获取token，无法下载数据")
                return None

            # 获取下载配置
            config = self._get_download_config(data_type)
            print(f"使用配置: {config.get('name', '未知类型')}")

            # 根据配置格式化日期
            date_format = config.get('date_format', 'number')
            formatted_begin_date = self._format_date_for_template(begin_date, date_format)
            formatted_end_date = self._format_date_for_template(end_date, date_format)

            if not formatted_begin_date or not formatted_end_date:
                print("日期格式错误")
                return None

            print(f"开始下载数据: {formatted_begin_date} 到 {formatted_end_date}")

            # 构建下载URL
            download_url = self._build_download_url(config, formatted_begin_date, formatted_end_date, token)
            print(f"下载URL: {download_url}")

            # 使用HTTP请求下载
            try:
                cookies = await self.context.cookies()
                cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])

                response = await self.page.request.get(
                    download_url,
                    headers={
                        'Cookie': cookie_str,
                        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                        'Referer': 'https://mp.weixin.qq.com/'
                    }
                )

                if response.status == 200:
                    file_content = await response.body()
                    print(f"数据下载成功，文件大小: {len(file_content)} bytes")
                    # 注意：这里不进行数据库导入
                    return file_content
                else:
                    print(f"下载失败，状态码: {response.status}")
                    error_text = await response.text()
                    print(f"错误响应: {error_text}")
                    return None

            except Exception as request_e:
                print(f"HTTP请求下载失败: {request_e}")
                # 尝试页面下载方法
                return await self._download_data_fallback_page_method_only(config, formatted_begin_date, formatted_end_date, token)

        except Exception as e:
            print(f"下载数据失败: {e}")
            return None

    async def _download_data_fallback_page_method(self, config: Dict[str, Any], begin_date: str, end_date: str, token: str, data_type: Optional[str] = None, auto_import: bool = True) -> Optional[bytes]:
        """备用下载方法：使用页面下载事件"""
        try:
            print("尝试页面下载方法...")

            # 构建下载URL
            download_url = self._build_download_url(config, begin_date, end_date, token)

            # 使用页面来监听下载事件
            async with self.page.expect_download() as download_info:
                # 触发下载
                await self.page.goto(download_url)

                # 等待下载完成
                download = await download_info.value

                # 读取下载的文件内容
                file_path = await download.path()
                if file_path:
                    with open(file_path, 'rb') as f:
                        file_content = f.read()

                    print(f"页面方法下载成功，文件大小: {len(file_content)} bytes")

                    # 自动导入数据到数据库
                    if auto_import and self.account_id and data_type:
                        await self._import_excel_to_database(file_content, data_type)

                    return file_content
                else:
                    print("页面方法下载文件路径为空")
                    return None

        except Exception as e:
            print(f"页面下载方法失败: {e}")
            return None

    async def _import_excel_to_database(self, excel_content: bytes, data_type: str):
        """将Excel数据导入到数据库"""
        try:
            print(f"开始导入数据，数据类型: {data_type}")
            print(f"Excel内容大小: {len(excel_content)} bytes")

            # 检查文件内容
            if len(excel_content) >= 4:
                file_header = excel_content[:4]
                print(f"文件头 (hex): {file_header.hex()}")

                # 检查是否是ZIP文件（Excel文件实际上是ZIP格式）
                if file_header[:2] == b'PK':
                    print("✅ 文件头显示这是一个ZIP/Excel文件")
                else:
                    print("❌ 文件头不是ZIP格式，可能不是有效的Excel文件")

                    # 尝试解码前200字节查看内容
                    preview_length = min(200, len(excel_content))
                    try:
                        text_preview = excel_content[:preview_length].decode('utf-8', errors='ignore')
                        print(f"文件内容预览: {repr(text_preview)}")

                        # 检查是否是HTML错误页面
                        if '<html>' in text_preview.lower() or '<!doctype' in text_preview.lower():
                            print("❌ 检测到HTML内容，服务器可能返回了错误页面")
                        elif 'error' in text_preview.lower():
                            print("❌ 检测到错误信息")
                    except Exception as decode_e:
                        print(f"无法解码文件内容: {decode_e}")

            # 导入数据明细服务
            from app.services.data_details_service import DataDetailsService
            from app.database import SessionLocal

            # 创建数据库会话
            db = SessionLocal()

            try:
                # 调用数据导入服务
                result = DataDetailsService.import_excel_data(
                    db=db,
                    account_id=self.account_id,
                    data_type=data_type,
                    excel_content=excel_content
                )

                if result["success"]:
                    # 提交事务
                    db.commit()
                    print(f"数据导入成功: 新增 {result['imported_count']} 条，更新 {result['updated_count']} 条")
                else:
                    # 回滚事务
                    db.rollback()
                    print(f"数据导入失败: {result['error']}")

            except Exception as e:
                # 发生异常时回滚事务
                db.rollback()
                print(f"数据导入过程中发生异常: {e}")
            finally:
                db.close()

        except Exception as e:
            print(f"数据导入过程中发生错误: {e}")

    async def fetch_user_source_data(self, begin_date: str, end_date: str) -> Optional[dict]:
        """获取用户来源数据

        Args:
            begin_date: 开始日期，格式: YYYY-MM-DD
            end_date: 结束日期，格式: YYYY-MM-DD

        Returns:
            用户来源数据字典，包含category_list字段
        """
        try:
            if not self.page:
                print("页面未初始化")
                return None

            # 获取token
            token = self._extract_token_from_url()
            if not token:
                print("无法获取token")
                return None

            print(f"开始获取用户来源数据: {begin_date} 到 {end_date}")

            # 用于存储拦截到的数据
            intercepted_data = None

            # 设置请求拦截器
            async def handle_route(route):
                nonlocal intercepted_data
                request_url = route.request.url

                # 使用正则表达式替换URL中的begin_date和end_date参数值
                import re
                # 替换begin_date参数 (格式: begin_date=yyyy-mm-dd)
                request_url = re.sub(r'begin_date=\d{4}-\d{2}-\d{2}', f'begin_date={begin_date}', request_url)
                # 替换end_date参数 (格式: end_date=yyyy-mm-dd)
                request_url = re.sub(r'end_date=\d{4}-\d{2}-\d{2}', f'end_date={end_date}', request_url)

                # 检查是否是我们要拦截的AJAX请求
                if ('useranalysis' in request_url and
                    'ajax=1' in request_url and
                    'f=json' in request_url):

                    print(f"拦截到用户分析AJAX请求: {request_url}")

                    try:
                        # 使用修改后的URL获取响应
                        response = await route.fetch(url=request_url)

                        # 读取响应数据
                        response_text = await response.text()
                        response_data = json.loads(response_text)

                        print(f"成功获取用户来源数据，数据大小: {len(response_text)} bytes")
                        intercepted_data = response_data

                        # 继续正常响应
                        await route.fulfill(response=response)

                    except Exception as e:
                        print(f"处理拦截请求时出错: {e}")
                        await route.continue_()
                else:
                    # 其他请求正常处理
                    await route.continue_()

            # 注册路由拦截器
            await self.page.route("**/*", handle_route)

            # 访问用户分析页面，这会触发AJAX请求
            analysis_url = f"https://mp.weixin.qq.com/misc/useranalysis?1=1&token={token}&lang=zh_CN"
            print(f"访问用户分析页面: {analysis_url}")

            await self.page.goto(analysis_url, wait_until="domcontentloaded")

            # 等待AJAX请求完成
            print("等待AJAX请求完成...")
            await self.page.wait_for_timeout(5000)  # 等待5秒

            # 移除路由拦截器
            await self.page.unroute("**/*")

            if intercepted_data:
                print("成功获取用户来源数据")
                return intercepted_data
            else:
                print("未能拦截到用户来源数据")
                return None

        except Exception as e:
            print(f"获取用户来源数据失败: {e}")
            return None

    def parse_and_save_user_source_data(self, data: dict, account_id: int) -> dict:
        """解析并保存用户来源数据

        Args:
            data: 从AJAX请求获取的数据
            account_id: 账号ID

        Returns:
            保存结果统计
        """
        try:
            from app.models import WeChatMPUserSource
            from app.database import SessionLocal
            from datetime import datetime, date

            # 创建数据库会话
            db = SessionLocal()

            try:
                category_list = data.get('category_list', [])
                if not category_list:
                    return {"success": False, "error": "数据中没有category_list字段"}

                imported_count = 0
                updated_count = 0

                for category in category_list:
                    user_source = category.get('user_source')
                    data_list = category.get('list', [])

                    if user_source is None:
                        continue

                    print(f"处理用户来源 {user_source}，数据条数: {len(data_list)}")

                    for item in data_list:
                        try:
                            # 解析日期
                            date_str = item.get('date')
                            if not date_str:
                                continue

                            # 转换日期格式 (假设是YYYY-MM-DD格式)
                            if isinstance(date_str, str):
                                item_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                            else:
                                continue

                            # 检查是否已存在记录
                            existing = db.query(WeChatMPUserSource).filter(
                                WeChatMPUserSource.account_id == account_id,
                                WeChatMPUserSource.user_source == user_source,
                                WeChatMPUserSource.date == item_date
                            ).first()

                            # 准备数据
                            record_data = {
                                'account_id': account_id,
                                'user_source': user_source,
                                'date': item_date,
                                'new_user': int(item.get('new_user', 0)),
                                'cancel_user': int(item.get('cancel_user', 0)),
                                'netgain_user': int(item.get('netgain_user', 0)),
                                'cumulate_user': int(item.get('cumulate_user', 0))
                            }

                            if existing:
                                # 更新现有记录
                                for key, value in record_data.items():
                                    if key != 'account_id':  # 不更新account_id
                                        setattr(existing, key, value)
                                existing.updated_at = datetime.utcnow()
                                updated_count += 1
                            else:
                                # 创建新记录
                                record = WeChatMPUserSource(**record_data)
                                db.add(record)
                                imported_count += 1

                        except Exception as item_e:
                            print(f"处理单条数据时出错: {item_e}, 数据: {item}")
                            continue

                # 提交事务
                db.commit()

                result = {
                    "success": True,
                    "imported_count": imported_count,
                    "updated_count": updated_count,
                    "total_processed": imported_count + updated_count
                }

                print(f"用户来源数据保存完成: 新增 {imported_count} 条，更新 {updated_count} 条")
                return result

            finally:
                db.close()

        except Exception as e:
            print(f"解析和保存用户来源数据失败: {e}")
            return {"success": False, "error": str(e)}

    def _format_date_for_download(self, date_str: str) -> Optional[str]:
        """格式化日期为下载所需的格式 YYYYMMDD"""
        try:
            # 去除可能的空格
            date_str = date_str.strip()

            # 如果已经是8位数字格式
            if len(date_str) == 8 and date_str.isdigit():
                return date_str

            # 如果是带分隔符的格式，尝试解析
            if '-' in date_str:
                parts = date_str.split('-')
            elif '/' in date_str:
                parts = date_str.split('/')
            else:
                return None

            if len(parts) == 3:
                year, month, day = parts
                # 确保年份是4位，月份和日期是1-2位数字
                if len(year) == 4 and year.isdigit() and month.isdigit() and day.isdigit():
                    return f"{year}{month.zfill(2)}{day.zfill(2)}"

            return None

        except Exception as e:
            print(f"日期格式化失败: {e}")
            return None

    async def get_cookies(self) -> Optional[str]:
        """获取登录后的cookies"""
        try:
            if not self.context:
                return None

            cookies = await self.context.cookies()
            return str(cookies)

        except Exception as e:
            print(f"获取cookies失败: {e}")
            return None

    async def get_user_summary_data(self, start_date: str, end_date: str) -> Optional[Dict[str, Any]]:
        """获取用户分析数据"""
        try:
            if not self.page:
                return None

            # 检查当前URL，如果不在微信公众号页面，先导航过去
            current_url = self.page.url
            if "mp.weixin.qq.com" not in current_url or "token=" not in current_url:
                print("当前不在微信公众号页面，正在导航到微信公众号主页...")
                await self.page.goto("https://mp.weixin.qq.com",
                                   wait_until="domcontentloaded", timeout=15000)
                await asyncio.sleep(3)  # 等待自动跳转完成

            # 导航到用户分析页面
            await self.page.goto("https://mp.weixin.qq.com/misc/appmsganalysis?action=report&token=" +
                                self._extract_token_from_url() + "&lang=zh_CN")

            # 等待页面加载
            await self.page.wait_for_selector("#main", timeout=10000)

            # 设置日期范围
            await self._set_date_range(start_date, end_date)

            # 等待数据加载
            await asyncio.sleep(3)

            # 提取用户数据
            user_data = await self._extract_user_data()

            return user_data

        except Exception as e:
            print(f"获取用户数据失败: {e}")
            return None

    async def get_article_summary_data(self, start_date: str, end_date: str) -> Optional[List[Dict[str, Any]]]:
        """获取图文分析数据"""
        try:
            if not self.page:
                return None

            # 检查当前URL，如果不在微信公众号页面，先导航过去
            current_url = self.page.url
            if "mp.weixin.qq.com" not in current_url or "token=" not in current_url:
                print("当前不在微信公众号页面，正在导航到微信公众号主页...")
                await self.page.goto("https://mp.weixin.qq.com",
                                   wait_until="domcontentloaded", timeout=15000)
                await asyncio.sleep(3)  # 等待自动跳转完成

            # 导航到图文分析页面
            await self.page.goto("https://mp.weixin.qq.com/misc/appmsganalysis?action=appmsgstat&token=" +
                                self._extract_token_from_url())

            # 等待页面加载
            await self.page.wait_for_selector("#main", timeout=10000)

            # 设置日期范围
            await self._set_date_range(start_date, end_date)

            # 等待数据加载
            await asyncio.sleep(3)

            # 提取图文数据
            article_data = await self._extract_article_data()

            return article_data

        except Exception as e:
            print(f"获取图文数据失败: {e}")
            return None

    def _extract_token_from_url(self) -> str:
        """从当前URL中提取token"""
        if not self.page:
            return ""

        current_url = self.page.url
        token_match = re.search(r'token=([^&]+)', current_url)
        return token_match.group(1) if token_match else ""

    async def _set_date_range(self, start_date: str, end_date: str):
        """设置日期范围"""
        try:
            # 这里需要根据实际的微信公众号后台页面结构来实现
            # 由于页面结构可能变化，这里提供一个基础框架
            pass
        except Exception as e:
            print(f"设置日期范围失败: {e}")

    async def _extract_user_data(self) -> Dict[str, Any]:
        """提取用户数据"""
        try:
            # 这里需要根据实际页面结构提取数据
            # 返回示例数据结构
            return {
                "new_users": 0,
                "cancel_users": 0,
                "net_growth": 0,
                "cumulative_users": 0,
                "date": datetime.now().strftime("%Y-%m-%d")
            }
        except Exception as e:
            print(f"提取用户数据失败: {e}")
            return {}

    async def _extract_article_data(self) -> List[Dict[str, Any]]:
        """提取图文数据"""
        try:
            # 这里需要根据实际页面结构提取数据
            # 返回示例数据结构
            return [
                {
                    "title": "示例文章",
                    "read_count": 0,
                    "like_count": 0,
                    "share_count": 0,
                    "publish_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            ]
        except Exception as e:
            print(f"提取图文数据失败: {e}")
            return []

    async def close(self):
        """关闭浏览器和清理资源"""
        try:
            if self.page:
                print("关闭页面...")
                # 先移除所有事件监听器，避免关闭时的错误
                try:
                    await self.page.remove_all_listeners()
                except Exception:
                    pass
                # 等待一下让正在进行的请求完成
                import asyncio
                await asyncio.sleep(0.5)
                await self.page.close()
                self.page = None
        except Exception as e:
            print(f"关闭页面失败: {e}")

        try:
            if self.context:
                print("关闭浏览器上下文...")
                from app.services.browser_manager import browser_manager
                await browser_manager.close_context(self.context)
                self.context = None
        except Exception as e:
            print(f"关闭浏览器上下文失败: {e}")

        # 已改为使用全局 BrowserManager，无需自管浏览器/playwright
        # 保留变量重置以兼容现有代码
        self.browser = None
        self.playwright = None

        print("资源清理完成")

    async def batch_download_data_excel(self, start_date: str, end_date: str,
                                      data_types: List[str] = None) -> Dict[str, Any]:
        """批量下载微信公众号数据Excel文件

        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            data_types: 数据类型列表，如果不提供则下载所有类型

        Returns:
            下载结果
        """
        if data_types is None:
            data_types = ['content_trend', 'content_source', 'content_detail', 'user_channel']

        try:
            # 检查登录状态
            if not await self.load_login_state():
                return {"success": False, "message": "登录状态恢复失败，请重新登录"}

            downloaded_files = []
            failed_files = []

            print(f"开始批量下载 {len(data_types)} 个文件")

            for i, data_type in enumerate(data_types):
                try:
                    print(f"下载文件 {i+1}/{len(data_types)}: {data_type}")

                    # 下载Excel数据
                    excel_data = await self.download_data_excel(
                        begin_date=start_date,
                        end_date=end_date,
                        data_type=data_type
                    )

                    if not excel_data:
                        failed_files.append({"data_type": data_type, "error": "下载数据失败"})
                        continue

                    downloaded_files.append({
                        "data_type": data_type,
                        "file_size": len(excel_data),
                        "success": True
                    })

                    print(f"文件 {data_type} 下载成功")

                    # 如果不是最后一个文件，等待1-2秒
                    if i < len(data_types) - 1:
                        import random
                        wait_time = 1 + random.random()  # 1-2秒随机等待
                        print(f"等待 {wait_time:.1f} 秒后下载下一个文件...")
                        await asyncio.sleep(wait_time)

                except Exception as e:
                    print(f"下载 {data_type} 失败: {e}")
                    failed_files.append({"data_type": data_type, "error": str(e)})

            # 下载完成后，获取用户来源数据
            user_source_result = None
            try:
                print("开始获取用户来源数据...")

                # 获取用户来源数据
                user_source_data = await self.fetch_user_source_data(
                    begin_date=start_date,
                    end_date=end_date
                )

                if user_source_data:
                    # 解析并保存用户来源数据
                    user_source_result = self.parse_and_save_user_source_data(
                        user_source_data, self.account_id
                    )

                    if user_source_result.get("success"):
                        print(f"用户来源数据获取成功: {user_source_result}")
                    else:
                        print(f"用户来源数据保存失败: {user_source_result.get('error')}")
                else:
                    print("未能获取到用户来源数据")

            except Exception as e:
                print(f"获取用户来源数据时出错: {e}")
                user_source_result = {"success": False, "error": str(e)}

            # 构建返回结果
            result = {
                "success": len(downloaded_files) > 0,
                "downloaded_count": len(downloaded_files),
                "failed_count": len(failed_files),
                "downloaded_files": downloaded_files,
                "failed_files": failed_files,
                "user_source_result": user_source_result
            }

            if len(failed_files) == 0:
                result["message"] = f"批量下载完成，成功: {len(downloaded_files)} 个"
            else:
                result["message"] = f"批量下载完成，成功: {len(downloaded_files)} 个，失败: {len(failed_files)} 个"

            return result

        except Exception as e:
            print(f"批量下载任务失败: {e}")
            return {"success": False, "message": f"批量下载任务失败: {str(e)}"}

    # 独立的数据类型下载方法
    async def download_content_trend_data(self, start_date: str, end_date: str, auto_import: bool = True):
        """下载内容趋势数据"""
        template = self.DOWNLOAD_TEMPLATES['content_trend']
        return await self.download_data_excel(
            begin_date=start_date,
            end_date=end_date,
            busi=template['busi'],
            tmpl=template['tmpl'],
            data_type='content_trend',
            auto_import=auto_import
        )

    async def download_content_source_data(self, start_date: str, end_date: str, auto_import: bool = True):
        """下载内容来源数据"""
        template = self.DOWNLOAD_TEMPLATES['content_source']
        return await self.download_data_excel(
            begin_date=start_date,
            end_date=end_date,
            busi=template['busi'],
            tmpl=template['tmpl'],
            data_type='content_source',
            auto_import=auto_import
        )

    async def download_content_detail_data(self, start_date: str, end_date: str, auto_import: bool = True):
        """下载内容详情数据"""
        template = self.DOWNLOAD_TEMPLATES['content_detail']
        return await self.download_data_excel(
            begin_date=start_date,
            end_date=end_date,
            busi=template['busi'],
            tmpl=template['tmpl'],
            data_type='content_detail',
            auto_import=auto_import
        )

    async def download_user_channel_data(self, start_date: str, end_date: str, auto_import: bool = True):
        """下载用户渠道数据"""
        template = self.DOWNLOAD_TEMPLATES['user_channel']
        # user_channel 使用特殊的 useranalysis 类型，不需要 busi 和 tmpl
        return await self.download_data_excel(
            begin_date=start_date,
            end_date=end_date,
            busi=None,  # useranalysis 类型不使用 busi
            tmpl=None,  # useranalysis 类型不使用 tmpl
            data_type='user_channel',
            auto_import=auto_import
        )

    async def download_user_source_data(self, start_date: str, end_date: str, auto_import: bool = True):
        """下载用户来源数据"""
        # user_source 使用特殊的 AJAX 获取方式，不通过 Excel 下载
        try:
            # 获取用户来源数据
            user_source_data = await self.fetch_user_source_data(
                begin_date=start_date,
                end_date=end_date
            )

            if user_source_data and auto_import:
                # 解析并保存用户来源数据
                result = self.parse_and_save_user_source_data(
                    user_source_data, self.account_id
                )

                if result.get("success"):
                    return user_source_data  # 返回原始数据
                else:
                    print(f"用户来源数据保存失败: {result.get('error')}")
                    return None
            else:
                return user_source_data

        except Exception as e:
            print(f"下载用户来源数据失败: {e}")
            return None

    # 统一接口方法
    async def download_single_data_type(
        self,
        data_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        auto_import: bool = True,
        **kwargs
    ) -> DataDownloadResult:
        """微信公众号统一数据下载入口"""
        method_map = {
            'content_trend': self.download_content_trend_data,
            'content_source': self.download_content_source_data,
            'content_detail': self.download_content_detail_data,
            'user_channel': self.download_user_channel_data,
            'user_source': self.download_user_source_data,
        }

        if data_type not in method_map:
            return DataDownloadResult(
                success=False,
                error_message=f"不支持的数据类型: {data_type}"
            )

        if not start_date or not end_date:
            return DataDownloadResult(
                success=False,
                error_message="微信公众号数据下载需要提供开始和结束日期"
            )

        try:
            method = method_map[data_type]
            result = await method(
                start_date=start_date,
                end_date=end_date,
                auto_import=auto_import
            )
            return DataDownloadResult(
                success=result is not None,
                data=result,
                error_message=None if result else "下载失败"
            )
        except Exception as e:
            return DataDownloadResult(
                success=False,
                error_message=f"下载失败: {str(e)}"
            )

    def get_supported_data_types(self) -> List[Tuple[str, str]]:
        """获取支持的数据类型"""
        return [
            ('content_trend', '内容趋势'),
            ('content_source', '内容来源'),
            ('content_detail', '内容详情'),
            ('user_channel', '用户渠道'),
            ('user_source', '用户来源')
        ]