# CentOS 8 原生部署指南

## 系统要求

- **操作系统**: CentOS 8.x
- **Python**: 3.11.13 (需要编译安装)
- **Node.js**: 24.x
- **内存**: 最低 4GB，推荐 8GB+
- **存储**: 最低 20GB 可用空间
- **CPU**: 2核心以上
- **用户权限**: sudo权限

## 快速部署

### 1. 准备工作

```bash
# 1. 上传项目文件到服务器
scp -r ./social-media-manager root@your-server:/tmp/

# 2. 登录服务器
ssh root@your-server

# 3. 移动项目到目标目录
mv /tmp/social-media-manager /home/<USER>/social-media-management
cd /home/<USER>/social-media-management

# 4. 运行部署脚本
chmod +x native-deploy.sh
./native-deploy.sh
```

### 2. 手动部署步骤

如果自动脚本遇到问题，可以按以下步骤手动部署：

#### 2.1 配置CentOS 8软件源

```bash
# 由于CentOS 8已停止维护，切换到vault源
sudo sed -i 's/mirrorlist/#mirrorlist/g' /etc/yum.repos.d/CentOS-*
sudo sed -i 's|#baseurl=http://mirror.centos.org|baseurl=http://vault.centos.org|g' /etc/yum.repos.d/CentOS-*

# 启用PowerTools仓库
sudo dnf config-manager --set-enabled powertools

# 安装EPEL仓库
sudo dnf install -y epel-release

# 更新系统
sudo dnf update -y
```

#### 2.2 安装系统依赖

```bash
# 安装开发工具
sudo dnf groupinstall -y "Development Tools"

# 安装系统依赖包
sudo dnf install -y \
    curl \
    wget \
    git \
    openssl-devel \
    libffi-devel \
    python38-devel \
    python38-pip \
    cairo-devel \
    gobject-introspection-devel \
    fontconfig \
    liberation-fonts \
    google-noto-cjk-fonts \
    alsa-lib \
    atk \
    cups-libs \
    dbus-glib \
    libdrm \
    gtk3 \
    libXcomposite \
    libXdamage \
    libXfixes \
    libXrandr \
    libXScrnSaver \
    libXtst \
    xorg-x11-server-Xvfb \
    nginx \
    supervisor \
    sqlite-devel \
    gcc-c++ \
    make

# 配置Python3符号链接
sudo alternatives --install /usr/bin/python3 python3 /usr/bin/python3.8 1
sudo alternatives --install /usr/bin/pip3 pip3 /usr/bin/pip3.8 1

# 更新字体缓存
sudo fc-cache -fv
```

#### 2.3 安装Python 3.11.13

```bash
# 安装编译依赖
sudo dnf install -y \
    gcc gcc-c++ make \
    zlib-devel bzip2-devel openssl-devel \
    ncurses-devel sqlite-devel readline-devel \
    tk-devel gdbm-devel db4-devel \
    libpcap-devel xz-devel expat-devel \
    libffi-devel libuuid-devel

# 下载并编译Python 3.11.13
cd /tmp
wget https://www.python.org/ftp/python/3.11.13/Python-3.11.13.tgz
tar -xzf Python-3.11.13.tgz
cd Python-3.11.13

# 配置编译选项
./configure --enable-optimizations --with-ensurepip=install --prefix=/usr/local

# 编译安装
make -j$(nproc)
sudo make altinstall

# 创建符号链接
sudo ln -sf /usr/local/bin/python3.11 /usr/local/bin/python3
sudo ln -sf /usr/local/bin/pip3.11 /usr/local/bin/pip3

# 更新PATH
echo 'export PATH="/usr/local/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc

# 清理编译文件
cd /
rm -rf /tmp/Python-3.11.13*

# 验证安装
python3 --version
```

#### 2.4 安装Node.js

```bash
# 安装Node.js 24
curl -fsSL https://rpm.nodesource.com/setup_24.x | sudo bash -
sudo dnf install -y nodejs

# 配置npm国内镜像
npm config set registry https://registry.npmmirror.com
npm config set disturl https://npmmirror.com/dist

# 验证安装
node --version
npm --version
```

#### 2.5 创建部署环境

```bash
# 创建web用户
sudo useradd -r -s /bin/bash -d /home/<USER>

# 设置目录权限
sudo chown -R web:web /home/<USER>/social-media-management
sudo chmod 755 /home/<USER>/social-media-management

# 创建必要目录
sudo -u web mkdir -p /home/<USER>/social-media-management/{user_data,data,temp_downloads,logs}
```

#### 2.6 部署后端

```bash
cd /home/<USER>/social-media-management

# 创建Python虚拟环境
sudo -u web /usr/local/bin/python3.11 -m venv venv

# 激活虚拟环境并安装依赖
sudo -u web bash -c "
    source venv/bin/activate
    pip install --upgrade pip
    pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple/
    pip install -r requirements.txt
"

# 安装Playwright浏览器
sudo -u web bash -c "
    source venv/bin/activate
    export PLAYWRIGHT_DOWNLOAD_HOST=https://npmmirror.com/mirrors/playwright/
    playwright install chromium
    playwright install-deps chromium
"
```

#### 2.7 部署前端

```bash
cd /home/<USER>/social-media-management/frontend

# 安装前端依赖
sudo -u web npm install

# 构建生产版本
sudo -u web npm run build
```

#### 2.8 配置环境变量

```bash
cd /home/<USER>/social-media-management

# 复制环境配置文件
sudo -u web cp .env.example .env

# 生成随机密钥并配置
SECRET_KEY=$(openssl rand -base64 32)
sudo -u web sed -i "s/your_secret_key_here_generate_a_new_one/$SECRET_KEY/" .env
sudo -u web sed -i "s|DATABASE_URL=sqlite:///./social_media.db|DATABASE_URL=sqlite:///home/<USER>/social-media-management/data/social_media.db|" .env
```

### 3. 配置服务

#### 3.1 配置Supervisor

创建后端服务配置：

```bash
sudo tee /etc/supervisord.d/social-media-backend.ini << EOF
[program:social-media-backend]
command=/home/<USER>/social-media-management/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000 --workers 1
directory=/home/<USER>/social-media-management
user=web
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/home/<USER>/social-media-management/logs/backend.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
environment=PYTHONPATH="/home/<USER>/social-media-management"
EOF
```

#### 3.2 配置Nginx

创建Nginx配置：

```bash
sudo tee /etc/nginx/conf.d/social-media.conf << EOF
server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名
    
    # 前端静态文件
    location / {
        root /home/<USER>/social-media-management/frontend/build;
        index index.html;
        try_files \$uri \$uri/ /index.html;
    }
    
    # 后端API
    location /api/ {
        proxy_pass http://127.0.0.1:8000/api/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:8000/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
    }
}
EOF
```

### 4. 启动服务

```bash
# 启动并启用Supervisor
sudo systemctl start supervisord
sudo systemctl enable supervisord

# 重新加载Supervisor配置
sudo supervisorctl reread
sudo supervisorctl update

# 启动后端服务
sudo supervisorctl start social-media-backend

# 启动并启用Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# 配置防火墙
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 5. 验证部署

```bash
# 检查服务状态
sudo supervisorctl status
sudo systemctl status nginx

# 检查端口监听
sudo netstat -tlnp | grep -E ':(80|8000)'

# 测试后端API
curl http://localhost:8000/

# 测试前端
curl http://localhost/
```

### 6. 日志查看

```bash
# 查看后端日志
sudo tail -f /home/<USER>/social-media-management/logs/backend.log

# 查看Supervisor日志
sudo supervisorctl tail -f social-media-backend

# 查看Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

## 常见问题

### 1. Python依赖安装失败

```bash
# 如果遇到编译错误，确保安装了所有开发工具
sudo dnf groupinstall -y "Development Tools"
sudo dnf install -y python38-devel openssl-devel libffi-devel
```

### 2. Playwright浏览器安装失败

```bash
# 手动安装系统依赖
sudo dnf install -y \
    alsa-lib atk cups-libs dbus-glib libdrm gtk3 \
    libXcomposite libXdamage libXfixes libXrandr \
    libXScrnSaver libXtst xorg-x11-server-Xvfb

# 重新安装Playwright
sudo -u web bash -c "
    source venv/bin/activate
    playwright install-deps chromium
    playwright install chromium
"
```

### 3. 权限问题

```bash
# 确保目录权限正确
sudo chown -R web:web /home/<USER>/social-media-management
sudo chmod -R 755 /home/<USER>/social-media-management/user_data
sudo chmod -R 755 /home/<USER>/social-media-management/data
sudo chmod -R 755 /home/<USER>/social-media-management/temp_downloads
```

### 4. 服务无法启动

```bash
# 检查配置文件语法
sudo nginx -t
sudo supervisorctl status

# 查看详细错误日志
sudo journalctl -u nginx -f
sudo supervisorctl tail social-media-backend
```

## 维护操作

### 更新代码

```bash
# 停止服务
sudo supervisorctl stop social-media-backend

# 更新代码
cd /home/<USER>/social-media-management
sudo -u web git pull  # 如果使用git

# 更新Python依赖
sudo -u web bash -c "source venv/bin/activate && pip install -r requirements.txt"

# 重新构建前端
cd frontend
sudo -u web npm install
sudo -u web npm run build

# 重启服务
sudo supervisorctl start social-media-backend
sudo systemctl reload nginx
```

### 备份数据

```bash
# 备份数据库
sudo -u web cp /home/<USER>/social-media-management/data/social_media.db \
    /home/<USER>/social-media-management/data/social_media.db.backup.$(date +%Y%m%d_%H%M%S)

# 备份用户数据
sudo tar -czf /home/<USER>/backup_$(date +%Y%m%d_%H%M%S).tar.gz \
    -C /home/<USER>/social-media-management user_data data
```
