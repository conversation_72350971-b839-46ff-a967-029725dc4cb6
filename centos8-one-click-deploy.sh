#!/bin/bash

# CentOS 8 一键部署脚本
# 社交媒体管理系统原生部署

set -e

# 配置变量
DEPLOY_PATH="/home/<USER>/social-media-management"
SERVICE_USER="web"
DOMAIN_NAME=""
PYTHON_VERSION="3.11.13"
NODE_VERSION="24"
PYTHON_VERSION="3.11.13"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查CentOS版本
check_centos() {
    if [ ! -f /etc/centos-release ]; then
        log_error "此脚本仅适用于CentOS系统"
        exit 1
    fi
    
    VERSION=$(cat /etc/centos-release | grep -oE '[0-9]+' | head -1)
    if [[ "$VERSION" != "8" ]]; then
        log_error "此脚本仅适用于CentOS 8"
        exit 1
    fi
    
    log_success "检测到CentOS 8系统"
}

# 配置软件源
setup_repos() {
    log_info "配置CentOS 8软件源..."
    
    # 备份原始源
    cp -r /etc/yum.repos.d /etc/yum.repos.d.backup
    
    # 切换到vault源
    sed -i 's/mirrorlist/#mirrorlist/g' /etc/yum.repos.d/CentOS-*
    sed -i 's|#baseurl=http://mirror.centos.org|baseurl=http://vault.centos.org|g' /etc/yum.repos.d/CentOS-*
    
    # 启用PowerTools仓库
    dnf config-manager --set-enabled powertools
    
    # 安装EPEL仓库
    dnf install -y epel-release
    
    # 更新系统
    dnf update -y
    
    log_success "软件源配置完成"
}

# 安装系统依赖
install_dependencies() {
    log_info "安装系统依赖..."
    
    # 安装开发工具
    dnf groupinstall -y "Development Tools"
    
    # 安装系统依赖包
    dnf install -y \
        curl \
        wget \
        git \
        openssl-devel \
        libffi-devel \
        python38-devel \
        python38-pip \
        cairo-devel \
        gobject-introspection-devel \
        fontconfig \
        liberation-fonts \
        google-noto-cjk-fonts \
        alsa-lib \
        atk \
        cups-libs \
        dbus-glib \
        libdrm \
        gtk3 \
        libXcomposite \
        libXdamage \
        libXfixes \
        libXrandr \
        libXScrnSaver \
        libXtst \
        xorg-x11-server-Xvfb \
        nginx \
        supervisor \
        sqlite-devel \
        gcc-c++ \
        make \
        firewalld
    
    # 配置Python3符号链接
    alternatives --install /usr/bin/python3 python3 /usr/bin/python3.8 1
    alternatives --install /usr/bin/pip3 pip3 /usr/bin/pip3.8 1
    
    # 更新字体缓存
    fc-cache -fv
    
    log_success "系统依赖安装完成"
}

# 安装Python 3.11.13
install_python() {
    log_info "安装Python $PYTHON_VERSION..."

    # 检查是否已安装正确版本
    if command -v python3.11 &> /dev/null; then
        CURRENT_VERSION=$(python3.11 --version | cut -d' ' -f2)
        if [[ "$CURRENT_VERSION" == "$PYTHON_VERSION" ]]; then
            log_success "Python $PYTHON_VERSION 已安装"
            return
        fi
    fi

    # 安装编译依赖
    dnf install -y \
        gcc \
        gcc-c++ \
        make \
        zlib-devel \
        bzip2-devel \
        openssl-devel \
        ncurses-devel \
        sqlite-devel \
        readline-devel \
        tk-devel \
        gdbm-devel \
        db4-devel \
        libpcap-devel \
        xz-devel \
        expat-devel \
        libffi-devel \
        libuuid-devel

    # 下载并编译Python
    cd /tmp
    wget https://www.python.org/ftp/python/$PYTHON_VERSION/Python-$PYTHON_VERSION.tgz
    tar -xzf Python-$PYTHON_VERSION.tgz
    cd Python-$PYTHON_VERSION

    # 配置编译选项
    ./configure --enable-optimizations --with-ensurepip=install --prefix=/usr/local

    # 编译安装
    make -j$(nproc)
    make altinstall

    # 创建符号链接
    ln -sf /usr/local/bin/python3.11 /usr/local/bin/python3
    ln -sf /usr/local/bin/pip3.11 /usr/local/bin/pip3

    # 更新PATH
    echo 'export PATH="/usr/local/bin:$PATH"' >> /etc/profile
    export PATH="/usr/local/bin:$PATH"

    # 清理编译文件
    cd /
    rm -rf /tmp/Python-$PYTHON_VERSION*

    # 验证安装
    /usr/local/bin/python3.11 --version

    log_success "Python $PYTHON_VERSION 安装完成"
}

# 安装Node.js
install_nodejs() {
    log_info "安装Node.js $NODE_VERSION..."

    # 安装Node.js 24
    curl -fsSL https://rpm.nodesource.com/setup_${NODE_VERSION}.x | bash -
    dnf install -y nodejs

    # 配置npm国内镜像
    npm config set registry https://registry.npmmirror.com
    npm config set disturl https://npmmirror.com/dist

    # 验证安装
    node --version
    npm --version

    log_success "Node.js安装完成"
}

# 创建用户和目录
setup_user_and_dirs() {
    log_info "创建用户和目录..."
    
    # 创建web用户
    if ! id "$SERVICE_USER" &>/dev/null; then
        useradd -r -s /bin/bash -d /home/<USER>
        log_info "创建用户: $SERVICE_USER"
    fi
    
    # 创建部署目录
    mkdir -p $DEPLOY_PATH
    chown -R $SERVICE_USER:$SERVICE_USER /home/<USER>
    chmod 755 /home/<USER>
    
    # 创建必要的子目录
    sudo -u $SERVICE_USER mkdir -p $DEPLOY_PATH/{user_data,data,temp_downloads,logs}
    
    log_success "用户和目录创建完成"
}

# 部署应用
deploy_application() {
    log_info "部署应用..."
    
    cd $DEPLOY_PATH
    
    # 检查项目文件是否存在
    if [ ! -f "requirements.txt" ]; then
        log_error "未找到requirements.txt文件，请确保项目文件已上传到 $DEPLOY_PATH"
        exit 1
    fi
    
    # 创建Python虚拟环境
    sudo -u $SERVICE_USER /usr/local/bin/python3.11 -m venv venv
    
    # 配置pip镜像
    sudo -u $SERVICE_USER mkdir -p /home/<USER>/.pip
    sudo -u $SERVICE_USER tee /home/<USER>/.pip/pip.conf << EOF
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple/
trusted-host = pypi.tuna.tsinghua.edu.cn
EOF
    
    # 安装Python依赖
    sudo -u $SERVICE_USER bash -c "
        source venv/bin/activate
        pip install --upgrade pip
        pip install -r requirements.txt
    "
    
    # 安装Playwright浏览器
    sudo -u $SERVICE_USER bash -c "
        source venv/bin/activate
        export PLAYWRIGHT_DOWNLOAD_HOST=https://npmmirror.com/mirrors/playwright/
        playwright install chromium
        playwright install-deps chromium
    "
    
    # 部署前端
    if [ -d "frontend" ]; then
        cd frontend
        sudo -u $SERVICE_USER npm install
        sudo -u $SERVICE_USER npm run build
        cd ..
    fi
    
    # 配置环境变量
    if [ ! -f ".env" ]; then
        sudo -u $SERVICE_USER cp .env.example .env
        
        # 生成随机密钥
        SECRET_KEY=$(openssl rand -base64 32)
        sudo -u $SERVICE_USER sed -i "s/your_secret_key_here_generate_a_new_one/$SECRET_KEY/" .env
        sudo -u $SERVICE_USER sed -i "s|DATABASE_URL=sqlite:///./social_media.db|DATABASE_URL=sqlite://$DEPLOY_PATH/data/social_media.db|" .env
    fi
    
    log_success "应用部署完成"
}

# 配置服务
configure_services() {
    log_info "配置系统服务..."
    
    # 配置Supervisor
    cp configs/supervisor-backend.ini /etc/supervisord.d/social-media.ini
    
    # 配置Nginx
    cp configs/nginx-social-media.conf /etc/nginx/conf.d/social-media.conf
    
    # 如果提供了域名，更新Nginx配置
    if [ -n "$DOMAIN_NAME" ]; then
        sed -i "s/your-domain.com/$DOMAIN_NAME/g" /etc/nginx/conf.d/social-media.conf
    fi
    
    # 测试Nginx配置
    nginx -t
    
    log_success "服务配置完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动并启用服务
    systemctl start supervisord
    systemctl enable supervisord
    
    systemctl start nginx
    systemctl enable nginx
    
    systemctl start firewalld
    systemctl enable firewalld
    
    # 重新加载Supervisor配置
    supervisorctl reread
    supervisorctl update
    
    # 启动应用服务
    supervisorctl start social-media-backend
    
    # 配置防火墙
    firewall-cmd --permanent --add-service=http
    firewall-cmd --permanent --add-service=https
    firewall-cmd --reload
    
    log_success "服务启动完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 等待服务启动
    sleep 10
    
    # 检查服务状态
    supervisorctl status
    systemctl status nginx --no-pager
    
    # 检查端口监听
    netstat -tlnp | grep -E ':(80|8000)'
    
    # 测试服务
    if curl -f http://localhost:8000/ > /dev/null 2>&1; then
        log_success "后端服务正常"
    else
        log_error "后端服务异常"
    fi
    
    if curl -f http://localhost/ > /dev/null 2>&1; then
        log_success "前端服务正常"
    else
        log_error "前端服务异常"
    fi
    
    log_success "部署验证完成"
}

# 显示部署结果
show_result() {
    echo ""
    echo "🎉 部署完成！"
    echo "=================================="
    echo "📱 访问地址: http://$(hostname -I | awk '{print $1}')"
    if [ -n "$DOMAIN_NAME" ]; then
        echo "📱 域名访问: http://$DOMAIN_NAME"
    fi
    echo ""
    echo "📊 服务管理命令:"
    echo "  查看状态: supervisorctl status"
    echo "  重启后端: supervisorctl restart social-media-backend"
    echo "  查看日志: tail -f $DEPLOY_PATH/logs/backend.log"
    echo ""
    echo "🔧 Nginx管理:"
    echo "  重启Nginx: systemctl restart nginx"
    echo "  查看日志: tail -f /var/log/nginx/social_media_error.log"
    echo ""
    echo "⚠️  注意事项:"
    echo "1. 请配置域名解析指向服务器IP"
    echo "2. 如需HTTPS，请配置SSL证书"
    echo "3. 定期备份数据库和用户数据"
}

# 主函数
main() {
    echo "🚀 CentOS 8 社交媒体管理系统一键部署"
    echo "======================================="
    
    # 获取域名（可选）
    read -p "请输入域名（可选，直接回车跳过）: " DOMAIN_NAME
    
    check_root
    check_centos
    setup_repos
    install_dependencies
    install_python
    install_nodejs
    setup_user_and_dirs
    deploy_application
    configure_services
    start_services
    verify_deployment
    show_result
}

# 执行主函数
main "$@"
