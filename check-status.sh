#!/bin/bash

echo "🔍 快速状态检查"
echo "================"

# 检查容器状态
echo "📦 Docker容器状态："
if command -v docker &> /dev/null; then
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(social_media|NAMES)"
else
    echo "❌ Docker未安装"
fi

echo ""

# 检查端口
echo "🌐 端口状态："
echo "端口8000（后端）："
if netstat -tlnp 2>/dev/null | grep :8000 | head -1; then
    echo "✅ 后端端口正常"
else
    echo "❌ 后端端口未监听"
fi

echo "端口3000（前端）："
if netstat -tlnp 2>/dev/null | grep :3000 | head -1; then
    echo "✅ 前端端口正常"
else
    echo "❌ 前端端口未监听"
fi

echo ""

# 检查服务响应
echo "🔗 服务响应测试："
echo -n "后端API: "
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/ 2>/dev/null | grep -q "200"; then
    echo "✅ 正常"
else
    echo "❌ 异常"
fi

echo -n "前端服务: "
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/ 2>/dev/null | grep -q "200"; then
    echo "✅ 正常"
else
    echo "❌ 异常"
fi

echo ""
echo "💡 如果有问题，运行以下命令："
echo "   ./diagnose-server.sh    # 详细诊断"
echo "   ./quick-fix-server.sh   # 快速修复"
echo "   docker-compose logs -f  # 查看日志"
