[program:social-media-backend]
command=/home/<USER>/social-media-management/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000 --workers 1
directory=/home/<USER>/social-media-management
user=web
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/home/<USER>/social-media-management/logs/backend.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile=/home/<USER>/social-media-management/logs/backend_error.log
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=10
environment=PYTHONPATH="/home/<USER>/social-media-management",PATH="/usr/local/bin:/home/<USER>/social-media-management/venv/bin:%(ENV_PATH)s"
startretries=3
startsecs=10
stopwaitsecs=30
killasgroup=true
stopasgroup=true

[program:social-media-scheduler]
command=/home/<USER>/social-media-management/venv/bin/python -c "
import sys
sys.path.insert(0, '/home/<USER>/social-media-management')
from app.services.auto_update_service import AutoUpdateService
import asyncio
import signal
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def signal_handler(signum, frame):
    logger.info('收到停止信号，正在关闭调度器...')
    AutoUpdateService.stop_scheduler()
    sys.exit(0)

signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)

logger.info('启动自动更新调度器...')
AutoUpdateService.start_scheduler()

try:
    while True:
        import time
        time.sleep(1)
except KeyboardInterrupt:
    logger.info('收到键盘中断，正在关闭调度器...')
    AutoUpdateService.stop_scheduler()
"
directory=/home/<USER>/social-media-management
user=web
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/home/<USER>/social-media-management/logs/scheduler.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile=/home/<USER>/social-media-management/logs/scheduler_error.log
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=10
environment=PYTHONPATH="/home/<USER>/social-media-management",PATH="/usr/local/bin:/home/<USER>/social-media-management/venv/bin:%(ENV_PATH)s"
startretries=3
startsecs=10
stopwaitsecs=30
killasgroup=true
stopasgroup=true
