#!/bin/bash

echo "🔍 社交媒体管理系统服务器诊断脚本"
echo "========================================"

# 检查Docker是否安装和运行
echo ""
echo "📦 检查Docker状态..."
if command -v docker &> /dev/null; then
    echo "✅ Docker已安装"
    if docker info &> /dev/null; then
        echo "✅ Docker服务正在运行"
    else
        echo "❌ Docker服务未运行，请启动Docker服务"
        echo "   sudo systemctl start docker"
        exit 1
    fi
else
    echo "❌ Docker未安装"
    exit 1
fi

# 检查Docker Compose是否安装
echo ""
echo "🔧 检查Docker Compose状态..."
if command -v docker-compose &> /dev/null; then
    echo "✅ Docker Compose已安装"
    docker-compose --version
elif docker compose version &> /dev/null; then
    echo "✅ Docker Compose (plugin)已安装"
    docker compose version
else
    echo "❌ Docker Compose未安装"
    exit 1
fi

# 检查项目文件
echo ""
echo "📁 检查项目文件..."
if [ -f "docker-compose.yml" ]; then
    echo "✅ docker-compose.yml存在"
else
    echo "❌ docker-compose.yml不存在"
    exit 1
fi

if [ -f "main.py" ]; then
    echo "✅ main.py存在"
else
    echo "❌ main.py不存在"
    exit 1
fi

if [ -d "frontend" ]; then
    echo "✅ frontend目录存在"
else
    echo "❌ frontend目录不存在"
fi

# 检查环境变量文件
echo ""
echo "🔐 检查环境配置..."
if [ -f ".env" ]; then
    echo "✅ .env文件存在"
    echo "📋 环境变量预览（隐藏敏感信息）："
    grep -E "^[A-Z_]+" .env | sed 's/=.*/=***/' | head -10
else
    echo "⚠️  .env文件不存在，将使用默认配置"
    if [ -f "docker-env-example.env" ]; then
        echo "💡 发现示例配置文件，建议复制并修改："
        echo "   cp docker-env-example.env .env"
    fi
fi

# 检查容器状态
echo ""
echo "🐳 检查Docker容器状态..."
if docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(social_media|backend|frontend)"; then
    echo ""
    echo "📊 详细容器信息："
    docker ps -a --filter "name=social_media" --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}"
else
    echo "⚠️  未找到相关容器，可能需要启动应用"
fi

# 检查端口占用
echo ""
echo "🌐 检查端口占用情况..."
echo "端口8000（后端API）："
if netstat -tlnp 2>/dev/null | grep :8000; then
    echo "✅ 端口8000有服务监听"
else
    echo "❌ 端口8000无服务监听"
fi

echo "端口3000（前端）："
if netstat -tlnp 2>/dev/null | grep :3000; then
    echo "✅ 端口3000有服务监听"
else
    echo "❌ 端口3000无服务监听"
fi

# 检查服务响应
echo ""
echo "🔗 检查服务响应..."
echo "测试后端API（端口8000）："
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/ | grep -q "200"; then
    echo "✅ 后端API响应正常"
    echo "📋 API信息："
    curl -s http://localhost:8000/ | head -1
else
    echo "❌ 后端API无响应或异常"
fi

echo "测试前端服务（端口3000）："
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/ | grep -q "200"; then
    echo "✅ 前端服务响应正常"
else
    echo "❌ 前端服务无响应或异常"
fi

# 检查日志
echo ""
echo "📝 最近的容器日志..."
if docker ps --filter "name=social_media_backend" --format "{{.Names}}" | grep -q social_media_backend; then
    echo "后端日志（最后10行）："
    docker logs --tail 10 social_media_backend 2>&1 | head -10
fi

if docker ps --filter "name=social_media_frontend" --format "{{.Names}}" | grep -q social_media_frontend; then
    echo "前端日志（最后10行）："
    docker logs --tail 10 social_media_frontend 2>&1 | head -10
fi

# 提供解决建议
echo ""
echo "🛠️  故障排除建议："
echo "========================================"

if ! docker ps --filter "name=social_media" --format "{{.Names}}" | grep -q social_media; then
    echo "1. 启动应用："
    echo "   docker-compose up -d"
    echo ""
fi

echo "2. 查看详细日志："
echo "   docker-compose logs -f"
echo ""

echo "3. 重新构建并启动："
echo "   docker-compose down"
echo "   docker-compose build --no-cache"
echo "   docker-compose up -d"
echo ""

echo "4. 检查防火墙设置："
echo "   sudo ufw status"
echo "   sudo ufw allow 3000"
echo "   sudo ufw allow 8000"
echo ""

echo "5. 如果是权限问题："
echo "   sudo chown -R \$(id -u):\$(id -g) ./user_data ./data ./temp_downloads"
echo ""

echo "6. 访问应用："
echo "   前端: http://localhost:3000"
echo "   后端API: http://localhost:8000"
echo "   API文档: http://localhost:8000/docs"

echo ""
echo "🎯 诊断完成！"
