# 社交媒体管理系统Docker环境配置示例
# 复制此文件为 .env 并根据需要修改配置

# 浏览器资源管理配置
# 最大并发浏览器上下文数量（建议根据服务器性能调整）
MAX_BROWSER_CONTEXTS=8

# 高优先级操作预留的上下文数量（用于用户交互操作如获取二维码）
PRIORITY_BROWSER_CONTEXTS=2

# 超时配置（毫秒）
# 默认超时时间
WECHAT_CHANNELS_DEFAULT_TIMEOUT=60000

# 页面导航超时时间（建议在Docker环境中设置更长）
WECHAT_CHANNELS_NAVIGATION_TIMEOUT=120000

# 网络连接检查超时时间
WECHAT_CHANNELS_NETWORK_CHECK_TIMEOUT=30000

# 重试配置
# 最大重试次数
WECHAT_CHANNELS_MAX_RETRIES=5

# 重试间隔基础时间（秒）
WECHAT_CHANNELS_RETRY_DELAY=10

# 调试模式（true/false）
WECHAT_CHANNELS_DEBUG=true

# 额外的浏览器启动参数（用逗号分隔）
# 例如：代理设置、额外的安全参数等
# WECHAT_CHANNELS_BROWSER_ARGS=--proxy-server=http://proxy:8080,--ignore-certificate-errors

# 示例：如果需要通过代理访问
# WECHAT_CHANNELS_BROWSER_ARGS=--proxy-server=http://your-proxy:8080

# 示例：如果需要忽略SSL证书错误
# WECHAT_CHANNELS_BROWSER_ARGS=--ignore-certificate-errors,--ignore-ssl-errors

# 示例：如果需要设置用户代理
# WECHAT_CHANNELS_BROWSER_ARGS=--user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
