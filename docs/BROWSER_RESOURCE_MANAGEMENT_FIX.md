# 浏览器资源管理死锁问题修复

## 问题描述

在运行数据更新任务时，同时在账户管理里登录账号获取二维码，一定几率会获取不到，发生长久的等待，没有结果。这个时候再执行任何和浏览器有关的操作似乎都是无法继续的，似乎发生了某种死锁。

## 问题分析

### 根本原因

1. **并发限制过小**：原始的 `BrowserManager` 只允许4个并发浏览器上下文，无法满足数据更新任务和用户交互操作同时进行的需求
2. **缺乏优先级机制**：用户交互操作（如获取登录二维码）与后台数据更新任务竞争相同的资源池
3. **无超时机制**：`create_context()` 会无限等待信号量，导致用户界面长时间无响应
4. **资源监控不足**：缺乏有效的资源使用监控和诊断工具

### 死锁场景

1. 数据更新任务启动，为多个账号创建浏览器上下文，占用了大部分或全部4个资源槽位
2. 用户同时尝试获取登录二维码，需要新的浏览器上下文
3. `await self._semaphore.acquire()` 无限等待，用户界面卡死
4. 如果数据更新任务中某个服务异常退出未正确调用 `close()`，信号量永远不会被释放

## 解决方案

### 1. 增加并发限制

```python
# 从4个增加到8个
max_contexts = int(os.getenv("MAX_BROWSER_CONTEXTS", "8"))
self._semaphore = asyncio.Semaphore(max_contexts)
```

### 2. 实现优先级机制

```python
# 为用户交互操作预留高优先级资源池
priority_contexts = int(os.getenv("PRIORITY_BROWSER_CONTEXTS", "2"))
self._priority_semaphore = asyncio.Semaphore(priority_contexts)
```

用户交互操作（如获取登录二维码）使用高优先级资源池：

```python
context = await browser_manager.create_context(
    priority=True,  # 高优先级
    timeout=30.0,   # 30秒超时
    operation_type="login_qrcode"
)
```

### 3. 添加超时机制

```python
try:
    await asyncio.wait_for(semaphore.acquire(), timeout=timeout)
except asyncio.TimeoutError:
    raise Exception(f"获取浏览器资源超时({timeout}s)，请稍后重试")
```

### 4. 改进资源监控

- 记录每个活跃上下文的创建时间、操作类型、优先级
- 提供资源使用状态查询API
- 支持清理长时间未使用的上下文
- 详细的日志记录

### 5. 更好的异常处理

确保无论创建上下文是否成功，都能正确释放信号量：

```python
try:
    context = await self.browser.new_context(...)
    # 记录上下文信息
    return context
except Exception as e:
    semaphore.release()  # 失败时释放信号量
    raise
```

## 配置选项

在 `.env` 文件中添加以下配置：

```bash
# 最大并发浏览器上下文数量（建议根据服务器性能调整）
MAX_BROWSER_CONTEXTS=8

# 高优先级操作预留的上下文数量（用于用户交互操作）
PRIORITY_BROWSER_CONTEXTS=2
```

## 监控API

新增浏览器资源监控API：

- `GET /api/browser/status` - 获取浏览器资源使用状态
- `POST /api/browser/cleanup` - 清理长时间未使用的浏览器上下文
- `GET /api/browser/log` - 记录当前活跃的浏览器上下文到日志

## 测试验证

运行测试脚本验证修复效果：

```bash
python test/test_browser_resource_management.py
```

测试包括：
- 并发上下文创建测试
- 优先级机制测试
- 超时机制测试
- 资源监控功能测试

## 使用建议

1. **服务器配置**：根据服务器性能调整 `MAX_BROWSER_CONTEXTS` 值
2. **监控使用**：定期检查 `/api/browser/status` 了解资源使用情况
3. **清理维护**：可以设置定时任务调用 `/api/browser/cleanup` 清理过期上下文
4. **日志监控**：关注浏览器相关的错误日志，及时发现资源泄漏问题

## 预期效果

- ✅ 用户获取登录二维码不再被数据更新任务阻塞
- ✅ 增加了超时机制，避免无限等待
- ✅ 提供了资源监控工具，便于问题诊断
- ✅ 改进了异常处理，减少资源泄漏风险
- ✅ 支持根据服务器性能灵活配置资源限制
