# 登录二维码上下文互斥机制修复

## 问题描述

从日志中发现：

```
social_media_backend_dev   | 2025-09-22 09:00:56 - app.services.browser_manager - INFO - 请求创建上下文 ctx_25: type=wechat_mp_login_qrcode, priority=True
social_media_backend_dev   | 2025-09-22 09:01:26 - app.services.browser_manager - ERROR - 获取浏览器上下文超时 ctx_25: type=wechat_mp_login_qrcode, timeout=30.0s
social_media_backend_dev   | 2025-09-22 09:01:26 - app.services.browser_manager - INFO - 当前活跃上下文数量: 2
social_media_backend_dev   | 2025-09-22 09:01:26 - app.services.browser_manager - INFO -   ctx_12: type=xiaohongshu_login_qrcode, priority=True, age=490.9s
social_media_backend_dev   | 2025-09-22 09:01:26 - app.services.browser_manager - INFO -   ctx_13: type=xiaohongshu_login_qrcode, priority=True, age=473.4s
```

**问题分析**：
1. 两个小红书登录二维码上下文（`ctx_12` 和 `ctx_13`）长时间占用高优先级资源池
2. 用户没有完成登录，但上下文一直存在（490.9s 和 473.4s）
3. 新的微信公众号登录请求无法获取资源，导致超时

**根本原因**：
- 登录二维码类型的上下文应该是互斥的，同一平台同时只需要一个
- 缺乏过期上下文的自动清理机制
- 资源泄漏导致高优先级资源池被耗尽

## 解决方案

### 1. 登录二维码上下文互斥机制

实现同平台登录二维码上下文的互斥逻辑：新的登录请求会自动清理同平台的旧上下文。

```python
async def create_context(self, *, operation_type: str = "unknown", **kwargs):
    # 检查是否为登录二维码类型，如果是则清理同类型的现有上下文
    if "login_qrcode" in operation_type:
        await self._cleanup_login_qrcode_contexts(operation_type)
    
    # 继续创建新上下文...
```

### 2. 同类型上下文清理逻辑

```python
async def _cleanup_login_qrcode_contexts(self, new_operation_type: str):
    """清理同类型的登录二维码上下文"""
    # 提取平台类型（如 wechat_mp_login_qrcode -> wechat_mp）
    platform = new_operation_type.replace("_login_qrcode", "")
    
    contexts_to_cleanup = []
    for context_id, info in self._active_contexts.items():
        # 检查是否为同平台的登录二维码上下文
        if ("login_qrcode" in info["operation_type"] and 
            info["operation_type"].startswith(platform)):
            contexts_to_cleanup.append((context_id, info))
    
    # 清理找到的上下文
    for context_id, info in contexts_to_cleanup:
        await info["context"].close()
        info["semaphore"].release()
        del self._active_contexts[context_id]
```

### 3. 过期上下文自动清理

添加清理长时间未使用上下文的机制：

```python
async def cleanup_expired_contexts(self, max_age_seconds: int = 600):
    """清理过期的上下文（默认10分钟）"""
    current_time = time.time()
    expired_contexts = []
    
    for context_id, info in self._active_contexts.items():
        age = current_time - info["created_at"]
        if age > max_age_seconds:
            expired_contexts.append((context_id, info, age))
    
    # 清理过期上下文
    for context_id, info, age in expired_contexts:
        await info["context"].close()
        info["semaphore"].release()
        del self._active_contexts[context_id]
```

### 4. API接口支持

添加清理过期上下文的API接口：

```python
@router.post("/browser/cleanup-expired")
async def cleanup_expired_contexts(max_age_seconds: int = 600):
    """清理过期的浏览器上下文"""
    cleaned_count = await browser_manager.cleanup_expired_contexts(max_age_seconds)
    return {
        "success": True,
        "message": f"已清理 {cleaned_count} 个过期上下文",
        "cleaned_count": cleaned_count
    }
```

## 修复效果

### 修复前的问题

- ❌ 同平台多个登录二维码上下文并存
- ❌ 长时间未使用的上下文占用资源
- ❌ 高优先级资源池被耗尽
- ❌ 新的登录请求超时失败
- ❌ 缺乏资源清理机制

### 修复后的改进

- ✅ 同平台登录二维码上下文互斥（新的替换旧的）
- ✅ 不同平台登录二维码上下文可以共存
- ✅ 自动清理过期上下文释放资源
- ✅ 高优先级资源池得到保护
- ✅ 登录请求响应时间改善
- ✅ 提供手动清理API接口

## 工作原理

### 1. 互斥逻辑

```
用户A请求小红书登录二维码 → 创建 ctx_1 (xiaohongshu_login_qrcode)
用户A再次请求小红书登录二维码 → 清理 ctx_1，创建 ctx_2 (xiaohongshu_login_qrcode)
用户B请求微信公众号登录二维码 → 创建 ctx_3 (wechat_mp_login_qrcode)，ctx_2 保留
```

### 2. 平台识别

通过操作类型识别平台：
- `wechat_mp_login_qrcode` → 平台：`wechat_mp`
- `xiaohongshu_login_qrcode` → 平台：`xiaohongshu`
- `wechat_channels_login_qrcode` → 平台：`wechat_channels`

### 3. 资源管理

- **创建时**：检查并清理同平台旧上下文
- **使用中**：正常占用资源
- **过期时**：可通过API或自动机制清理
- **完成时**：用户主动关闭或系统清理

## 测试验证

### 自动化测试

运行测试脚本验证修复效果：

```bash
python test/test_login_qrcode_context_exclusion.py
```

### 测试场景

1. **同平台互斥测试**：
   - 创建第一个微信公众号登录二维码上下文
   - 创建第二个微信公众号登录二维码上下文
   - 验证第一个被自动清理

2. **不同平台共存测试**：
   - 创建微信公众号登录二维码上下文
   - 创建小红书登录二维码上下文
   - 验证两个上下文可以共存

3. **过期清理测试**：
   - 创建多个上下文
   - 等待一段时间
   - 调用清理接口
   - 验证过期上下文被清理

### 手动测试

1. **登录流程测试**：
   - 打开小红书账号管理页面
   - 点击获取登录二维码
   - 不完成登录，再次点击获取二维码
   - 验证新二维码正常显示

2. **资源监控测试**：
   - 访问 `/api/browser/status` 查看资源状态
   - 调用 `/api/browser/cleanup-expired` 清理过期上下文
   - 验证资源正确释放

## 配置参数

### 环境变量

```bash
# 最大并发浏览器上下文数量
MAX_BROWSER_CONTEXTS=8

# 高优先级操作预留的上下文数量
PRIORITY_BROWSER_CONTEXTS=2
```

### API参数

```bash
# 清理过期上下文
POST /api/browser/cleanup-expired
{
  "max_age_seconds": 600  # 过期时间（秒），默认10分钟
}
```

## 最佳实践

### 1. 资源管理

- 登录二维码操作使用高优先级资源
- 数据更新等后台任务使用普通资源
- 定期清理过期上下文释放资源

### 2. 用户体验

- 新的登录请求立即可用
- 避免"资源不足"的错误提示
- 提供清晰的状态反馈

### 3. 系统稳定性

- 防止资源泄漏
- 避免资源池耗尽
- 提供监控和诊断工具

## 相关文件

- `app/services/browser_manager.py` - 主要修复文件
- `app/routers/browser_monitor.py` - 监控API接口
- `test/test_login_qrcode_context_exclusion.py` - 测试脚本
- `docs/BROWSER_RESOURCE_MANAGEMENT_FIX.md` - 相关文档

## 总结

这次修复通过实现登录二维码上下文的互斥机制和过期清理功能，彻底解决了资源泄漏和资源池耗尽的问题。现在同一平台的登录二维码请求会自动替换旧的上下文，确保资源的高效利用和用户体验的提升。
