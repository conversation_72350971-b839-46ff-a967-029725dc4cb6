# 服务器部署故障排除指南

## 问题现象

```bash
wget http://localhost:3000
# 输出：Connection reset by peer
```

## 问题分析

这个错误表明端口3000上没有服务正在监听，或者服务启动失败。这是一个前后端分离的应用：

- **后端**：FastAPI应用，运行在端口8000
- **前端**：React应用，运行在端口3000（通过nginx提供服务）

## 快速诊断

### 1. 使用诊断脚本

```bash
# 运行诊断脚本
./diagnose-server.sh
```

### 2. 手动检查步骤

#### 检查Docker服务
```bash
# 检查Docker是否运行
sudo systemctl status docker

# 如果未运行，启动Docker
sudo systemctl start docker
```

#### 检查容器状态
```bash
# 查看所有容器
docker ps -a

# 查看项目相关容器
docker ps -a | grep social_media
```

#### 检查端口占用
```bash
# 检查端口3000
netstat -tlnp | grep :3000

# 检查端口8000
netstat -tlnp | grep :8000
```

## 常见问题及解决方案

### 1. 容器未启动

**现象**：`docker ps` 没有显示相关容器

**解决方案**：
```bash
# 启动应用
docker-compose up -d

# 查看启动日志
docker-compose logs -f
```

### 2. 容器启动失败

**现象**：容器状态显示 `Exited`

**解决方案**：
```bash
# 查看失败日志
docker-compose logs backend
docker-compose logs frontend

# 重新构建并启动
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### 3. 权限问题

**现象**：日志显示权限错误

**解决方案**：
```bash
# 修复目录权限
sudo chown -R $(id -u):$(id -g) ./user_data ./data ./temp_downloads
chmod -R 755 ./user_data ./data ./temp_downloads
```

### 4. 环境配置问题

**现象**：容器启动但服务异常

**解决方案**：
```bash
# 检查.env文件
ls -la .env

# 如果不存在，从示例创建
cp docker-env-example.env .env

# 编辑配置
nano .env
```

### 5. 端口冲突

**现象**：端口被占用

**解决方案**：
```bash
# 查找占用端口的进程
sudo lsof -i :3000
sudo lsof -i :8000

# 停止冲突的服务或修改docker-compose.yml中的端口映射
```

## 快速修复

### 使用快速修复脚本

```bash
# 运行快速修复脚本
./quick-fix-server.sh
```

### 手动快速修复

```bash
# 1. 停止所有容器
docker-compose down

# 2. 清理资源
docker container prune -f
docker image prune -f

# 3. 修复权限
sudo chown -R $(id -u):$(id -g) user_data data temp_downloads
chmod -R 755 user_data data temp_downloads

# 4. 重新构建和启动
docker-compose build --no-cache
docker-compose up -d

# 5. 查看状态
docker-compose ps
docker-compose logs -f
```

## 验证部署成功

### 检查服务响应

```bash
# 检查后端API
curl http://localhost:8000/
# 应该返回：{"message": "社交媒体数据管理系统"}

# 检查前端服务
curl -I http://localhost:3000/
# 应该返回：HTTP/1.1 200 OK

# 检查API文档
curl http://localhost:8000/docs
```

### 访问应用

- **前端应用**：http://localhost:3000
- **后端API**：http://localhost:8000
- **API文档**：http://localhost:8000/docs

## 日志分析

### 查看实时日志
```bash
# 所有服务日志
docker-compose logs -f

# 特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
```

### 常见错误模式

1. **权限错误**：
   ```
   PermissionError: [Errno 13] Permission denied
   ```

2. **端口占用**：
   ```
   Error starting userland proxy: listen tcp 0.0.0.0:3000: bind: address already in use
   ```

3. **依赖缺失**：
   ```
   ModuleNotFoundError: No module named 'xxx'
   ```

4. **数据库连接失败**：
   ```
   sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError)
   ```

## 防火墙配置

如果是远程服务器，确保防火墙允许相应端口：

```bash
# Ubuntu/Debian
sudo ufw allow 3000
sudo ufw allow 8000

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --reload
```

## 性能优化

### 资源监控
```bash
# 查看容器资源使用
docker stats

# 查看系统资源
htop
df -h
```

### 浏览器资源配置

在 `.env` 文件中调整：
```bash
# 根据服务器性能调整
MAX_BROWSER_CONTEXTS=8
PRIORITY_BROWSER_CONTEXTS=2
```

## 联系支持

如果问题仍然存在，请提供以下信息：

1. 操作系统版本：`uname -a`
2. Docker版本：`docker --version`
3. 容器状态：`docker-compose ps`
4. 错误日志：`docker-compose logs`
5. 系统资源：`free -h && df -h`
