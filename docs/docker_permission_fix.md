# Docker权限问题解决方案

## 🚨 问题描述

在服务器上运行Docker容器时遇到权限错误：
```
social_media_backend | PermissionError: [Errno 13] Permission denied: '/app/user_data/wechat_account_1'
```

## 🔍 问题原因

1. **容器内外用户权限不匹配**：Docker容器内的`appuser`用户无法在挂载的目录中创建子目录
2. **宿主机目录权限不正确**：挂载的`user_data`目录权限设置不当
3. **目录不存在**：必要的数据目录在宿主机上不存在

## 🚀 快速解决方案

### 方案一：使用快速修复脚本（推荐）

在项目根目录执行：

```bash
# 下载并运行快速修复脚本
./fix_docker_permissions.sh
```

### 方案二：手动修复

```bash
# 1. 停止容器
docker-compose down

# 2. 创建必要目录
mkdir -p user_data data temp_downloads

# 3. 设置正确权限
sudo chown -R $(id -u):$(id -g) user_data data temp_downloads
chmod -R 755 user_data data temp_downloads

# 4. 重新启动
docker-compose up -d

# 5. 检查日志
docker-compose logs -f backend
```

### 方案三：使用Python修复工具

```bash
# 运行权限修复工具
python tools/fix_permissions.py
```

## 🔧 详细解决步骤

### 1. 检查当前状态

```bash
# 检查目录权限
ls -la | grep -E "(user_data|data|temp_downloads)"

# 检查容器状态
docker-compose ps

# 查看容器日志
docker-compose logs backend
```

### 2. 创建和设置目录

```bash
# 创建必要目录
mkdir -p user_data data temp_downloads

# 设置权限（替换为你的用户ID）
chown -R 1000:1000 user_data data temp_downloads
chmod -R 755 user_data data temp_downloads
```

### 3. 重建容器（如果需要）

```bash
# 完全重建
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

## 🛠️ 预防措施

### 1. 更新的Dockerfile

已经更新了Dockerfile来预创建目录：

```dockerfile
# 创建必要的数据目录
RUN mkdir -p /app/user_data /app/data /app/temp_downloads

# 设置目录权限
RUN chown -R appuser:appuser /app
RUN chmod -R 755 /app/user_data /app/data /app/temp_downloads
```

### 2. 更新的启动脚本

`start.sh`现在包含权限检查：

```bash
# 创建必要的目录并设置权限
mkdir -p /app/user_data /app/data /app/temp_downloads

# 检查目录权限并尝试修复
if [ ! -w "/app/user_data" ]; then
    echo "⚠️  /app/user_data 目录无写权限，尝试修复..."
    # 权限修复逻辑
fi
```

### 3. Docker Compose配置

确保docker-compose.yml中的挂载配置正确：

```yaml
volumes:
  - ./user_data:/app/user_data
  - ./data:/app/data
  - ./temp_downloads:/app/temp_downloads
```

## 🔍 故障排除

### 问题1：权限仍然不正确

**解决方案：**
```bash
# 检查用户ID
id

# 在容器内检查
docker exec -it social_media_backend id

# 确保ID匹配，如果不匹配：
sudo chown -R $(id -u):$(id -g) user_data data temp_downloads
```

### 问题2：容器无法启动

**解决方案：**
```bash
# 查看详细日志
docker-compose logs --tail=50 backend

# 重建镜像
docker-compose build --no-cache backend

# 清理并重启
docker-compose down -v
docker-compose up -d
```

### 问题3：SELinux权限问题（CentOS/RHEL）

**解决方案：**
```bash
# 检查SELinux状态
getenforce

# 如果是Enforcing，设置SELinux标签
sudo chcon -Rt svirt_sandbox_file_t user_data data temp_downloads

# 或者临时禁用SELinux
sudo setenforce 0
```

## 📋 验证修复

### 1. 检查目录权限

```bash
# 检查权限
ls -la user_data data temp_downloads

# 测试写权限
echo "test" > user_data/test.txt && rm user_data/test.txt && echo "✅ 权限正常"
```

### 2. 检查容器状态

```bash
# 容器状态
docker-compose ps

# 应用健康检查
curl -f http://localhost:8000/ && echo "✅ 应用正常"
```

### 3. 测试功能

```bash
# 进入容器测试
docker exec -it social_media_backend bash

# 在容器内测试创建目录
mkdir -p /app/user_data/test_dir && echo "✅ 容器内权限正常"
```

## 🚨 紧急修复命令

如果服务器上遇到紧急情况，可以直接执行：

```bash
# 一键修复命令
docker-compose down && \
mkdir -p user_data data temp_downloads && \
sudo chown -R $(id -u):$(id -g) user_data data temp_downloads && \
chmod -R 755 user_data data temp_downloads && \
docker-compose up -d && \
echo "✅ 修复完成"
```

## 📞 获取帮助

如果问题仍然存在：

1. **检查系统日志**：`journalctl -u docker`
2. **查看Docker日志**：`docker-compose logs -f`
3. **检查磁盘空间**：`df -h`
4. **验证Docker版本**：`docker --version`

## 🔄 定期维护

建议定期执行以下检查：

```bash
# 每周检查权限
./fix_docker_permissions.sh

# 清理无用的Docker资源
docker system prune -f

# 检查日志大小
du -sh /var/lib/docker/containers/*/*-json.log
```
