#!/bin/bash

# 紧急权限修复脚本 - 一键解决Docker权限问题
# 使用方法: ./emergency_fix.sh

echo "🚨 紧急权限修复工具"
echo "===================="

# 停止容器
echo "1️⃣ 停止Docker容器..."
docker-compose down

# 创建目录
echo "2️⃣ 创建必要目录..."
mkdir -p user_data data temp_downloads

# 修复权限
echo "3️⃣ 修复目录权限..."
sudo chown -R $(id -u):$(id -g) user_data data temp_downloads
chmod -R 755 user_data data temp_downloads

# 启动容器
echo "4️⃣ 启动Docker容器..."
docker-compose up -d

# 等待启动
echo "5️⃣ 等待服务启动..."
sleep 10

# 检查状态
echo "6️⃣ 检查服务状态..."
docker-compose ps

echo
echo "🎉 修复完成！"
echo "📋 如果问题仍然存在，请查看日志:"
echo "   docker-compose logs -f backend"
