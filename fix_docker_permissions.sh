#!/bin/bash

# Docker权限快速修复脚本
# 解决 "Permission denied: '/app/user_data/wechat_account_1'" 错误

set -e

echo "🔧 Docker权限快速修复工具"
echo "=================================="
echo

# 检查是否在项目根目录
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    echo "💡 应该包含 docker-compose.yml 文件"
    exit 1
fi

echo "📁 当前目录: $(pwd)"
echo "👤 当前用户: $(whoami) (UID: $(id -u), GID: $(id -g))"
echo

# 1. 创建必要的目录
echo "1️⃣ 创建必要的目录..."
mkdir -p user_data data temp_downloads
echo "✅ 目录创建完成"

# 2. 设置目录权限
echo
echo "2️⃣ 设置目录权限..."

# 获取当前用户的UID和GID
CURRENT_UID=$(id -u)
CURRENT_GID=$(id -g)

echo "设置所有者为: UID $CURRENT_UID, GID $CURRENT_GID"

# 设置权限
chown -R $CURRENT_UID:$CURRENT_GID user_data data temp_downloads
chmod -R 755 user_data data temp_downloads

echo "✅ 权限设置完成"

# 3. 检查权限
echo
echo "3️⃣ 验证权限设置..."
echo "📋 目录权限信息:"
ls -la | grep -E "(user_data|data|temp_downloads)"

# 测试写权限
echo
echo "🧪 测试写权限..."
test_file="user_data/permission_test.txt"
if echo "test" > "$test_file" 2>/dev/null; then
    rm -f "$test_file"
    echo "✅ 写权限测试通过"
else
    echo "❌ 写权限测试失败"
    echo "💡 可能需要使用 sudo 运行此脚本"
fi

# 4. 检查Docker容器状态
echo
echo "4️⃣ 检查Docker容器状态..."
if command -v docker >/dev/null 2>&1; then
    if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q social_media; then
        echo "📦 发现运行中的容器:"
        docker ps --format "table {{.Names}}\t{{.Status}}" | grep social_media
        
        echo
        echo "🤔 是否重启容器以应用权限更改？"
        read -p "重启容器? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "🔄 重启容器..."
            docker-compose restart
            echo "✅ 容器重启完成"
            
            echo
            echo "📋 等待容器启动..."
            sleep 5
            
            echo "📊 检查容器状态:"
            docker-compose ps
            
            echo
            echo "📝 查看最新日志:"
            docker-compose logs --tail=20 backend
        else
            echo "ℹ️  跳过容器重启"
            echo "💡 请手动重启: docker-compose restart"
        fi
    else
        echo "ℹ️  未发现运行中的相关容器"
    fi
else
    echo "⚠️  Docker命令不可用"
fi

echo
echo "🎉 权限修复完成！"
echo
echo "📋 如果问题仍然存在，请尝试："
echo "   1. 完全重建容器: docker-compose down && docker-compose build --no-cache && docker-compose up -d"
echo "   2. 检查容器日志: docker-compose logs -f backend"
echo "   3. 进入容器检查: docker exec -it social_media_backend bash"
echo
echo "🔍 常见问题排查："
echo "   • 确保宿主机目录权限正确"
echo "   • 检查Docker挂载配置"
echo "   • 验证容器内用户权限"
echo
