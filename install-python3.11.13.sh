#!/bin/bash

# Python 3.11.13 编译安装脚本
# 适用于CentOS 8

set -e

PYTHON_VERSION="3.11.13"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    if [ ! -f /etc/centos-release ]; then
        log_error "此脚本仅适用于CentOS系统"
        exit 1
    fi
    
    VERSION=$(cat /etc/centos-release | grep -oE '[0-9]+' | head -1)
    if [[ "$VERSION" != "8" ]]; then
        log_error "此脚本仅适用于CentOS 8"
        exit 1
    fi
    
    log_success "检测到CentOS 8系统"
}

# 检查是否已安装
check_existing() {
    if command -v python3.11 &> /dev/null; then
        CURRENT_VERSION=$(python3.11 --version | cut -d' ' -f2)
        if [[ "$CURRENT_VERSION" == "$PYTHON_VERSION" ]]; then
            log_success "Python $PYTHON_VERSION 已安装"
            python3.11 --version
            exit 0
        fi
    fi
}

# 安装编译依赖
install_dependencies() {
    log_info "安装编译依赖..."
    
    # 确保使用vault源
    sed -i 's/mirrorlist/#mirrorlist/g' /etc/yum.repos.d/CentOS-*
    sed -i 's|#baseurl=http://mirror.centos.org|baseurl=http://vault.centos.org|g' /etc/yum.repos.d/CentOS-*
    
    # 启用PowerTools仓库
    dnf config-manager --set-enabled powertools || true
    
    # 安装EPEL仓库
    dnf install -y epel-release
    
    # 更新系统
    dnf update -y
    
    # 安装编译依赖
    dnf install -y \
        gcc \
        gcc-c++ \
        make \
        wget \
        tar \
        zlib-devel \
        bzip2-devel \
        openssl-devel \
        ncurses-devel \
        sqlite-devel \
        readline-devel \
        tk-devel \
        gdbm-devel \
        db4-devel \
        libpcap-devel \
        xz-devel \
        expat-devel \
        libffi-devel \
        libuuid-devel
    
    log_success "编译依赖安装完成"
}

# 下载Python源码
download_python() {
    log_info "下载Python $PYTHON_VERSION 源码..."
    
    cd /tmp
    
    # 清理可能存在的旧文件
    rm -rf Python-$PYTHON_VERSION*
    
    # 下载源码
    wget https://www.python.org/ftp/python/$PYTHON_VERSION/Python-$PYTHON_VERSION.tgz
    
    # 验证下载
    if [ ! -f "Python-$PYTHON_VERSION.tgz" ]; then
        log_error "Python源码下载失败"
        exit 1
    fi
    
    # 解压
    tar -xzf Python-$PYTHON_VERSION.tgz
    
    if [ ! -d "Python-$PYTHON_VERSION" ]; then
        log_error "Python源码解压失败"
        exit 1
    fi
    
    log_success "Python源码下载完成"
}

# 编译安装Python
compile_python() {
    log_info "编译安装Python $PYTHON_VERSION..."
    
    cd /tmp/Python-$PYTHON_VERSION
    
    # 配置编译选项
    log_info "配置编译选项..."
    ./configure \
        --enable-optimizations \
        --with-ensurepip=install \
        --prefix=/usr/local \
        --enable-shared \
        --with-system-ffi \
        --with-computed-gotos \
        --enable-loadable-sqlite-extensions
    
    # 编译（使用所有CPU核心）
    log_info "开始编译（这可能需要10-20分钟）..."
    make -j$(nproc)
    
    # 安装
    log_info "安装Python..."
    make altinstall
    
    # 配置动态链接库
    echo "/usr/local/lib" > /etc/ld.so.conf.d/python3.11.conf
    ldconfig
    
    log_success "Python编译安装完成"
}

# 配置Python环境
configure_python() {
    log_info "配置Python环境..."
    
    # 创建符号链接
    ln -sf /usr/local/bin/python3.11 /usr/local/bin/python3
    ln -sf /usr/local/bin/pip3.11 /usr/local/bin/pip3
    
    # 更新系统PATH
    echo 'export PATH="/usr/local/bin:$PATH"' > /etc/profile.d/python3.11.sh
    chmod +x /etc/profile.d/python3.11.sh
    
    # 立即生效
    export PATH="/usr/local/bin:$PATH"
    
    # 升级pip
    /usr/local/bin/python3.11 -m pip install --upgrade pip
    
    # 配置pip使用国内镜像
    mkdir -p /root/.pip
    cat > /root/.pip/pip.conf << EOF
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple/
trusted-host = pypi.tuna.tsinghua.edu.cn
EOF
    
    log_success "Python环境配置完成"
}

# 验证安装
verify_installation() {
    log_info "验证安装..."
    
    # 验证Python版本
    INSTALLED_VERSION=$(/usr/local/bin/python3.11 --version | cut -d' ' -f2)
    if [[ "$INSTALLED_VERSION" == "$PYTHON_VERSION" ]]; then
        log_success "Python版本验证通过: $INSTALLED_VERSION"
    else
        log_error "Python版本验证失败: 期望 $PYTHON_VERSION, 实际 $INSTALLED_VERSION"
        exit 1
    fi
    
    # 验证pip
    /usr/local/bin/pip3.11 --version
    
    # 测试基本功能
    /usr/local/bin/python3.11 -c "import sys; print('Python安装路径:', sys.executable)"
    /usr/local/bin/python3.11 -c "import ssl; print('SSL支持: OK')"
    /usr/local/bin/python3.11 -c "import sqlite3; print('SQLite支持: OK')"
    
    log_success "安装验证完成"
}

# 清理临时文件
cleanup() {
    log_info "清理临时文件..."
    
    cd /
    rm -rf /tmp/Python-$PYTHON_VERSION*
    
    log_success "清理完成"
}

# 显示安装结果
show_result() {
    echo ""
    echo "🎉 Python $PYTHON_VERSION 安装完成！"
    echo "=================================="
    echo "📍 安装路径: /usr/local/bin/python3.11"
    echo "📍 pip路径: /usr/local/bin/pip3.11"
    echo ""
    echo "🔧 使用方法:"
    echo "  python3.11 --version"
    echo "  pip3.11 --version"
    echo ""
    echo "🔗 符号链接已创建:"
    echo "  /usr/local/bin/python3 -> python3.11"
    echo "  /usr/local/bin/pip3 -> pip3.11"
    echo ""
    echo "⚠️  注意事项:"
    echo "1. 请重新登录或执行 'source /etc/profile' 使PATH生效"
    echo "2. 创建虚拟环境: python3.11 -m venv myenv"
    echo "3. pip已配置使用清华大学镜像源"
}

# 主函数
main() {
    echo "🚀 Python $PYTHON_VERSION 编译安装脚本"
    echo "======================================="
    
    check_root
    check_system
    check_existing
    install_dependencies
    download_python
    compile_python
    configure_python
    verify_installation
    cleanup
    show_result
}

# 执行主函数
main "$@"
