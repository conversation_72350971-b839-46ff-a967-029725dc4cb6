#!/bin/bash

# 社交媒体管理系统 - 原生部署脚本（不使用Docker）
# 适用于直接在服务器上安装Python和Node.js环境

set -e

# 配置变量
DEPLOY_PATH="/home/<USER>/social-media-management"
PYTHON_VERSION="3.11.13"
NODE_VERSION="24"
SERVICE_USER="web"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统类型
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    elif type lsb_release >/dev/null 2>&1; then
        OS=$(lsb_release -si)
        VER=$(lsb_release -sr)
    else
        OS=$(uname -s)
        VER=$(uname -r)
    fi
    log_info "检测到操作系统: $OS $VER"
}

# 安装系统依赖
install_system_deps() {
    log_info "安装系统依赖..."

    # CentOS 8 专用配置
    if [[ "$OS" == *"CentOS"* ]] && [[ "$VER" == "8"* ]]; then
        log_info "配置CentOS 8软件源..."

        # 由于CentOS 8已停止维护，需要切换到vault源
        sudo sed -i 's/mirrorlist/#mirrorlist/g' /etc/yum.repos.d/CentOS-*
        sudo sed -i 's|#baseurl=http://mirror.centos.org|baseurl=http://vault.centos.org|g' /etc/yum.repos.d/CentOS-*

        # 启用PowerTools仓库
        sudo dnf config-manager --set-enabled powertools

        # 安装EPEL仓库
        sudo dnf install -y epel-release

        # 更新系统
        sudo dnf update -y

        # 安装开发工具
        sudo dnf groupinstall -y "Development Tools"

        # 安装系统依赖
        sudo dnf install -y \
            curl \
            wget \
            git \
            openssl-devel \
            libffi-devel \
            python38-devel \
            python38-pip \
            cairo-devel \
            gobject-introspection-devel \
            fontconfig \
            liberation-fonts \
            google-noto-cjk-fonts \
            alsa-lib \
            atk \
            cups-libs \
            dbus-glib \
            libdrm \
            gtk3 \
            libXcomposite \
            libXdamage \
            libXfixes \
            libXrandr \
            libXScrnSaver \
            libXtst \
            xorg-x11-server-Xvfb \
            nginx \
            supervisor \
            sqlite-devel \
            gcc-c++ \
            make

        # 创建python3符号链接
        sudo alternatives --install /usr/bin/python3 python3 /usr/bin/python3.8 1
        sudo alternatives --install /usr/bin/pip3 pip3 /usr/bin/pip3.8 1

    elif [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        sudo apt update
        sudo apt install -y \
            curl \
            wget \
            git \
            build-essential \
            libssl-dev \
            libffi-dev \
            python3-dev \
            python3-pip \
            python3-venv \
            pkg-config \
            libcairo2-dev \
            libgirepository1.0-dev \
            fonts-liberation \
            fonts-wqy-zenhei \
            fonts-wqy-microhei \
            fonts-noto-cjk \
            fontconfig \
            libasound2 \
            libatk-bridge2.0-0 \
            libatk1.0-0 \
            libatspi2.0-0 \
            libcups2 \
            libdbus-1-3 \
            libdrm2 \
            libgtk-3-0 \
            libnspr4 \
            libnss3 \
            libxcomposite1 \
            libxdamage1 \
            libxfixes3 \
            libxrandr2 \
            libxss1 \
            libxtst6 \
            xvfb \
            nginx \
            supervisor
    else
        log_error "不支持的操作系统: $OS"
        exit 1
    fi

    # 更新字体缓存
    sudo fc-cache -fv

    log_success "系统依赖安装完成"
}

# 安装Python
install_python() {
    log_info "检查Python版本..."

    if command -v python3 &> /dev/null; then
        CURRENT_PYTHON=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
        log_info "当前Python版本: $CURRENT_PYTHON"

        # CentOS 8默认Python 3.8，符合要求
        if [[ "$CURRENT_PYTHON" == "3.8" ]] || [[ "$CURRENT_PYTHON" == "$PYTHON_VERSION" ]]; then
            log_success "Python版本符合要求"
            return
        fi
    fi

    log_info "配置Python环境..."

    if [[ "$OS" == *"CentOS"* ]] && [[ "$VER" == "8"* ]]; then
        # CentOS 8需要编译安装Python 3.11.13
        log_info "编译安装Python $PYTHON_VERSION..."

        # 安装编译依赖
        sudo dnf install -y \
            gcc \
            gcc-c++ \
            make \
            zlib-devel \
            bzip2-devel \
            openssl-devel \
            ncurses-devel \
            sqlite-devel \
            readline-devel \
            tk-devel \
            gdbm-devel \
            db4-devel \
            libpcap-devel \
            xz-devel \
            expat-devel \
            libffi-devel \
            libuuid-devel

        # 下载Python源码
        cd /tmp
        wget https://www.python.org/ftp/python/$PYTHON_VERSION/Python-$PYTHON_VERSION.tgz
        tar -xzf Python-$PYTHON_VERSION.tgz
        cd Python-$PYTHON_VERSION

        # 配置编译选项
        ./configure --enable-optimizations --with-ensurepip=install --prefix=/usr/local

        # 编译安装（使用多核心加速）
        make -j$(nproc)
        sudo make altinstall

        # 创建符号链接
        sudo ln -sf /usr/local/bin/python3.11 /usr/local/bin/python3
        sudo ln -sf /usr/local/bin/pip3.11 /usr/local/bin/pip3

        # 更新PATH
        echo 'export PATH="/usr/local/bin:$PATH"' >> ~/.bashrc
        export PATH="/usr/local/bin:$PATH"

        # 清理编译文件
        cd /
        rm -rf /tmp/Python-$PYTHON_VERSION*

        # 配置pip使用国内镜像
        mkdir -p ~/.pip
        cat > ~/.pip/pip.conf << EOF
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple/
trusted-host = pypi.tuna.tsinghua.edu.cn
EOF

    elif [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        # 添加deadsnakes PPA获取最新Python版本
        sudo apt install -y software-properties-common
        sudo add-apt-repository -y ppa:deadsnakes/ppa
        sudo apt update
        sudo apt install -y python${PYTHON_VERSION} python${PYTHON_VERSION}-venv python${PYTHON_VERSION}-dev

        # 创建符号链接
        sudo ln -sf /usr/bin/python${PYTHON_VERSION} /usr/local/bin/python3
    fi

    log_success "Python配置完成"
}

# 安装Node.js
install_nodejs() {
    log_info "检查Node.js版本..."

    if command -v node &> /dev/null; then
        CURRENT_NODE=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        log_info "当前Node.js版本: v$CURRENT_NODE"

        if [[ "$CURRENT_NODE" -ge "$NODE_VERSION" ]]; then
            log_success "Node.js版本符合要求"
            return
        fi
    fi

    log_info "安装Node.js $NODE_VERSION..."

    if [[ "$OS" == *"CentOS"* ]] && [[ "$VER" == "8"* ]]; then
        # 使用NodeSource仓库安装Node.js (CentOS版本)
        curl -fsSL https://rpm.nodesource.com/setup_${NODE_VERSION}.x | sudo bash -
        sudo dnf install -y nodejs

    elif [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        # 使用NodeSource仓库安装Node.js (Ubuntu/Debian版本)
        curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | sudo -E bash -
        sudo apt-get install -y nodejs
    fi

    # 配置npm使用国内镜像
    npm config set registry https://registry.npmmirror.com
    npm config set disturl https://npmmirror.com/dist

    # 验证安装
    node --version
    npm --version

    log_success "Node.js安装完成"
}

# 创建部署目录和用户
setup_deploy_env() {
    log_info "设置部署环境..."
    
    # 创建部署目录
    sudo mkdir -p $DEPLOY_PATH
    
    # 创建服务用户（如果不存在）
    if ! id "$SERVICE_USER" &>/dev/null; then
        sudo useradd -r -s /bin/bash -d $DEPLOY_PATH $SERVICE_USER
        log_info "创建服务用户: $SERVICE_USER"
    fi
    
    # 设置目录权限
    sudo chown -R $SERVICE_USER:$SERVICE_USER $DEPLOY_PATH
    sudo chmod 755 $DEPLOY_PATH
    
    # 创建必要的子目录
    sudo -u $SERVICE_USER mkdir -p $DEPLOY_PATH/{user_data,data,temp_downloads,logs}
    
    log_success "部署环境设置完成"
}

# 部署后端
deploy_backend() {
    log_info "部署后端服务..."
    
    cd $DEPLOY_PATH
    
    # 创建Python虚拟环境
    sudo -u $SERVICE_USER /usr/local/bin/python3.11 -m venv venv
    
    # 激活虚拟环境并安装依赖
    sudo -u $SERVICE_USER bash -c "
        source venv/bin/activate
        pip install --upgrade pip
        pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple/
        pip install -r requirements.txt
    "
    
    # 安装Playwright浏览器
    sudo -u $SERVICE_USER bash -c "
        source venv/bin/activate
        export PLAYWRIGHT_DOWNLOAD_HOST=https://npmmirror.com/mirrors/playwright/
        playwright install chromium
        playwright install-deps chromium
    "
    
    log_success "后端部署完成"
}

# 部署前端
deploy_frontend() {
    log_info "部署前端服务..."
    
    cd $DEPLOY_PATH/frontend
    
    # 安装前端依赖
    sudo -u $SERVICE_USER npm install
    
    # 构建生产版本
    sudo -u $SERVICE_USER npm run build
    
    log_success "前端部署完成"
}

# 配置环境变量
setup_env() {
    log_info "配置环境变量..."
    
    if [ ! -f $DEPLOY_PATH/.env ]; then
        sudo -u $SERVICE_USER cp $DEPLOY_PATH/.env.example $DEPLOY_PATH/.env
        
        # 生成随机密钥
        SECRET_KEY=$(openssl rand -base64 32)
        
        sudo -u $SERVICE_USER sed -i "s/your_secret_key_here_generate_a_new_one/$SECRET_KEY/" $DEPLOY_PATH/.env
        sudo -u $SERVICE_USER sed -i "s|DATABASE_URL=sqlite:///./social_media.db|DATABASE_URL=sqlite:///$DEPLOY_PATH/data/social_media.db|" $DEPLOY_PATH/.env
        
        log_info "请编辑 $DEPLOY_PATH/.env 文件配置其他参数"
    fi
    
    log_success "环境变量配置完成"
}

# 主函数
main() {
    log_info "开始原生部署社交媒体管理系统..."
    
    detect_os
    install_system_deps
    install_python
    install_nodejs
    setup_deploy_env
    
    if [ -f "$DEPLOY_PATH/requirements.txt" ]; then
        deploy_backend
    else
        log_warning "未找到requirements.txt，请先上传项目文件"
    fi
    
    if [ -d "$DEPLOY_PATH/frontend" ]; then
        deploy_frontend
    else
        log_warning "未找到frontend目录，请先上传项目文件"
    fi
    
    setup_env
    
    log_success "部署完成！"
    log_info "接下来请："
    log_info "1. 配置Nginx反向代理"
    log_info "2. 配置Supervisor进程管理"
    log_info "3. 启动服务"
}

# 执行主函数
main "$@"
