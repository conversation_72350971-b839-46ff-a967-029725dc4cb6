#!/bin/bash

echo "🚀 社交媒体管理系统快速修复脚本"
echo "=================================="

# 设置错误时退出
set -e

# 检查是否在项目根目录
if [ ! -f "docker-compose.yml" ] || [ ! -f "main.py" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

echo ""
echo "🛑 停止现有容器..."
docker-compose down || true

echo ""
echo "🧹 清理Docker资源..."
# 清理停止的容器
docker container prune -f || true
# 清理未使用的镜像
docker image prune -f || true

echo ""
echo "📁 检查和修复目录权限..."
# 创建必要的目录
mkdir -p user_data data temp_downloads

# 修复权限
sudo chown -R $(id -u):$(id -g) user_data data temp_downloads || {
    echo "⚠️  无法修改权限，尝试不使用sudo..."
    chown -R $(id -u):$(id -g) user_data data temp_downloads || true
}

# 设置目录权限
chmod -R 755 user_data data temp_downloads

echo ""
echo "🔧 检查环境配置..."
if [ ! -f ".env" ]; then
    if [ -f "docker-env-example.env" ]; then
        echo "📋 创建.env文件..."
        cp docker-env-example.env .env
        echo "✅ 已从示例文件创建.env"
    else
        echo "📝 创建基本.env文件..."
        cat > .env << 'EOF'
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=social_media

# JWT配置
SECRET_KEY=your-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# 应用配置
ENVIRONMENT=production
HOT_RELOAD=false

# 浏览器资源管理配置
MAX_BROWSER_CONTEXTS=8
PRIORITY_BROWSER_CONTEXTS=2

# 前端配置
FRONTEND_PORT=3000
FRONTEND_INTERNAL_PORT=80
NODE_ENV=production
EOF
        echo "✅ 已创建基本.env文件"
    fi
fi

echo ""
echo "🏗️  重新构建镜像..."
docker-compose build --no-cache

echo ""
echo "🚀 启动服务..."
docker-compose up -d

echo ""
echo "⏳ 等待服务启动..."
sleep 10

echo ""
echo "🔍 检查服务状态..."
docker-compose ps

echo ""
echo "📊 检查服务健康状态..."

# 检查后端
echo "检查后端API..."
for i in {1..30}; do
    if curl -s http://localhost:8000/ > /dev/null 2>&1; then
        echo "✅ 后端API启动成功"
        break
    else
        echo "⏳ 等待后端启动... ($i/30)"
        sleep 2
    fi
    
    if [ $i -eq 30 ]; then
        echo "❌ 后端启动超时"
        echo "📝 后端日志："
        docker-compose logs backend | tail -20
    fi
done

# 检查前端
echo "检查前端服务..."
for i in {1..30}; do
    if curl -s http://localhost:3000/ > /dev/null 2>&1; then
        echo "✅ 前端服务启动成功"
        break
    else
        echo "⏳ 等待前端启动... ($i/30)"
        sleep 2
    fi
    
    if [ $i -eq 30 ]; then
        echo "❌ 前端启动超时"
        echo "📝 前端日志："
        docker-compose logs frontend | tail -20
    fi
done

echo ""
echo "🎯 修复完成！"
echo "=================================="
echo ""
echo "📱 访问地址："
echo "   前端应用: http://localhost:3000"
echo "   后端API:  http://localhost:8000"
echo "   API文档:  http://localhost:8000/docs"
echo ""
echo "🔧 常用命令："
echo "   查看日志: docker-compose logs -f"
echo "   重启服务: docker-compose restart"
echo "   停止服务: docker-compose down"
echo ""
echo "📊 当前容器状态："
docker-compose ps

# 如果服务仍然有问题，显示详细日志
if ! curl -s http://localhost:3000/ > /dev/null 2>&1; then
    echo ""
    echo "⚠️  前端服务可能仍有问题，显示详细日志："
    echo "=================================="
    docker-compose logs frontend | tail -50
fi

if ! curl -s http://localhost:8000/ > /dev/null 2>&1; then
    echo ""
    echo "⚠️  后端服务可能仍有问题，显示详细日志："
    echo "=================================="
    docker-compose logs backend | tail -50
fi
