#!/bin/bash

# 智能启动脚本 - 根据环境变量决定是否启用热重载

set -e

echo "🚀 启动社交媒体管理系统..."

# 创建必要的目录并设置权限
echo "📁 检查和创建必要目录..."

# 创建数据目录
mkdir -p /app/user_data
mkdir -p /app/data
mkdir -p /app/temp_downloads

# 检查目录权限并尝试修复
echo "🔧 检查目录权限..."

# 检查是否有写权限
if [ ! -w "/app/user_data" ]; then
    echo "⚠️  /app/user_data 目录无写权限，尝试修复..."
    # 如果是root用户，修改权限
    if [ "$(id -u)" = "0" ]; then
        chown -R appuser:appuser /app/user_data /app/data /app/temp_downloads
        chmod -R 755 /app/user_data /app/data /app/temp_downloads
        echo "✅ 权限修复完成"
    else
        echo "❌ 当前用户无权限修复，请检查Docker挂载目录权限"
        echo "💡 建议在宿主机执行: sudo chown -R $(id -u):$(id -g) ./user_data ./data ./temp_downloads"
    fi
fi

# 测试创建子目录
echo "🧪 测试目录权限..."
test_dir="/app/user_data/permission_test"
if mkdir -p "$test_dir" 2>/dev/null; then
    rmdir "$test_dir" 2>/dev/null || true
    echo "✅ 目录权限正常"
else
    echo "❌ 目录权限异常，可能影响应用功能"
    echo "📋 当前用户: $(whoami) (UID: $(id -u), GID: $(id -g))"
    echo "📋 目录权限: $(ls -la /app/ | grep user_data)"
fi

# 检查是否启用热重载
if [ "${HOT_RELOAD:-false}" = "true" ]; then
    echo "🔥 启用热重载模式"
    echo "📁 监控目录: /app"
    echo "⚡ 代码变更将自动重启服务"
    
    # 启用热重载
    exec uvicorn main:app \
        --host 0.0.0.0 \
        --port 8000 \
        --reload \
        --reload-dir /app \
        --reload-exclude "*.pyc" \
        --reload-exclude "__pycache__" \
        --reload-exclude "*.log" \
        --reload-exclude "temp_downloads" \
        --reload-exclude "user_data" \
        --reload-exclude "data"
else
    echo "🏭 生产模式启动"
    echo "📦 使用预编译代码"
    
    # 生产模式
    exec uvicorn main:app \
        --host 0.0.0.0 \
        --port 8000 \
        --workers 1
fi
