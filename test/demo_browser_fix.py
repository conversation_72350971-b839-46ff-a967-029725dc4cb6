"""
演示浏览器资源管理修复效果
模拟数据更新任务和用户登录操作的并发场景
"""
import asyncio
import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.browser_manager import browser_manager


async def simulate_data_update_task(account_id: int, duration: int = 10):
    """模拟数据更新任务"""
    print(f"📊 开始数据更新任务 - 账号 {account_id}")
    
    try:
        # 创建浏览器上下文（普通优先级）
        context = await browser_manager.create_context(
            operation_type=f"data_update_account_{account_id}",
            priority=False,
            timeout=30.0
        )
        
        print(f"✅ 账号 {account_id} 数据更新任务获得浏览器资源")
        
        # 模拟数据更新过程
        await asyncio.sleep(duration)
        
        # 清理资源
        await browser_manager.close_context(context)
        print(f"🏁 账号 {account_id} 数据更新任务完成")
        
    except Exception as e:
        print(f"❌ 账号 {account_id} 数据更新任务失败: {e}")


async def simulate_user_login(user_id: int):
    """模拟用户登录获取二维码"""
    print(f"👤 用户 {user_id} 请求获取登录二维码")
    
    try:
        start_time = time.time()
        
        # 创建浏览器上下文（高优先级）
        context = await browser_manager.create_context(
            operation_type=f"user_login_{user_id}",
            priority=True,  # 用户操作使用高优先级
            timeout=30.0
        )
        
        response_time = time.time() - start_time
        print(f"🎯 用户 {user_id} 获得浏览器资源，响应时间: {response_time:.2f}秒")
        
        # 模拟获取二维码过程
        await asyncio.sleep(2)
        
        # 清理资源
        await browser_manager.close_context(context)
        print(f"✅ 用户 {user_id} 登录二维码获取成功")
        
    except Exception as e:
        print(f"❌ 用户 {user_id} 登录失败: {e}")


async def monitor_resources():
    """监控资源使用情况"""
    while True:
        await asyncio.sleep(3)
        status = browser_manager.get_resource_status()
        print(f"📈 资源状态: 活跃上下文={status['total_contexts']}, "
              f"普通池剩余={status['normal_available']}, "
              f"优先级池剩余={status['priority_available']}")


async def main():
    """主演示函数"""
    print("🚀 开始浏览器资源管理修复效果演示")
    print("=" * 60)
    
    # 启动浏览器管理器
    await browser_manager.start()
    
    # 启动资源监控
    monitor_task = asyncio.create_task(monitor_resources())
    
    try:
        # 场景1: 正常情况下的并发操作
        print("\n📋 场景1: 正常并发操作")
        print("-" * 30)
        
        tasks = []
        
        # 启动3个数据更新任务
        for i in range(3):
            task = asyncio.create_task(simulate_data_update_task(i + 1, 8))
            tasks.append(task)
        
        # 等待1秒，让数据更新任务先占用资源
        await asyncio.sleep(1)
        
        # 用户尝试登录（应该能够快速响应）
        user_task = asyncio.create_task(simulate_user_login(1))
        tasks.append(user_task)
        
        # 等待用户登录完成
        await user_task
        
        print("\n📋 场景2: 资源紧张情况")
        print("-" * 30)
        
        # 启动更多数据更新任务，占用大部分资源
        for i in range(5):
            task = asyncio.create_task(simulate_data_update_task(i + 4, 6))
            tasks.append(task)
        
        await asyncio.sleep(1)
        
        # 多个用户同时尝试登录
        user_tasks = []
        for i in range(3):
            user_task = asyncio.create_task(simulate_user_login(i + 2))
            user_tasks.append(user_task)
        
        # 等待用户登录完成
        await asyncio.gather(*user_tasks)
        
        print("\n📋 场景3: 超时测试")
        print("-" * 30)
        
        # 占用所有资源
        occupy_tasks = []
        for i in range(10):  # 超过总资源数
            try:
                task = asyncio.create_task(simulate_data_update_task(i + 10, 15))
                occupy_tasks.append(task)
            except:
                break
        
        await asyncio.sleep(2)
        
        # 尝试创建新任务，应该快速超时而不是无限等待
        print("🔄 尝试在资源耗尽时创建新任务...")
        try:
            await simulate_user_login(999)
        except Exception as e:
            print(f"✅ 正确处理了资源耗尽情况: {e}")
        
        # 等待所有任务完成
        print("\n⏳ 等待所有任务完成...")
        await asyncio.gather(*tasks, return_exceptions=True)
        await asyncio.gather(*occupy_tasks, return_exceptions=True)
        
    finally:
        # 停止监控
        monitor_task.cancel()
        
        # 最终资源状态
        final_status = browser_manager.get_resource_status()
        print(f"\n📊 最终资源状态: 活跃上下文={final_status['total_contexts']}")
        
        # 清理
        await browser_manager.stop()
        
        print("\n🎉 演示完成！")
        print("=" * 60)
        print("修复效果总结:")
        print("✅ 用户登录操作不再被数据更新任务阻塞")
        print("✅ 高优先级资源池确保用户操作快速响应")
        print("✅ 超时机制防止无限等待")
        print("✅ 资源监控提供实时状态信息")


if __name__ == "__main__":
    asyncio.run(main())
