"""
测试浏览器资源管理的改进
"""
import asyncio
import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.browser_manager import browser_manager


class TestBrowserResourceManagement:
    """浏览器资源管理测试"""

    async def test_concurrent_context_creation(self):
        """测试并发上下文创建"""
        print("测试并发上下文创建...")
        
        # 启动浏览器管理器
        await browser_manager.start()
        
        # 创建多个并发上下文
        contexts = []
        start_time = time.time()
        
        try:
            # 创建6个普通优先级上下文
            tasks = []
            for i in range(6):
                task = browser_manager.create_context(
                    operation_type=f"test_normal_{i}",
                    priority=False,
                    timeout=10.0
                )
                tasks.append(task)
            
            contexts = await asyncio.gather(*tasks)
            creation_time = time.time() - start_time
            
            print(f"成功创建 {len(contexts)} 个上下文，耗时: {creation_time:.2f}秒")
            
            # 检查资源状态
            status = browser_manager.get_resource_status()
            print(f"当前活跃上下文数量: {status['total_contexts']}")
            print(f"普通资源池剩余: {status['normal_available']}")
            print(f"优先级资源池剩余: {status['priority_available']}")
            
            assert len(contexts) == 6
            assert status['total_contexts'] == 6
            
        finally:
            # 清理资源
            for context in contexts:
                if context:
                    await browser_manager.close_context(context)
    
    async def test_priority_context_creation(self):
        """测试优先级上下文创建"""
        print("测试优先级上下文创建...")
        
        await browser_manager.start()
        
        # 先占用所有普通资源
        normal_contexts = []
        try:
            # 创建足够多的普通上下文来占用资源
            for i in range(6):  # 假设普通资源池有6个
                try:
                    context = await browser_manager.create_context(
                        operation_type=f"test_occupy_{i}",
                        priority=False,
                        timeout=5.0
                    )
                    normal_contexts.append(context)
                except Exception as e:
                    print(f"创建普通上下文 {i} 失败: {e}")
                    break
            
            print(f"已占用 {len(normal_contexts)} 个普通上下文")
            
            # 现在尝试创建高优先级上下文，应该仍然成功
            priority_context = None
            start_time = time.time()
            
            priority_context = await browser_manager.create_context(
                operation_type="test_priority",
                priority=True,
                timeout=10.0
            )
            
            priority_time = time.time() - start_time
            print(f"高优先级上下文创建成功，耗时: {priority_time:.2f}秒")
            
            # 检查资源状态
            status = browser_manager.get_resource_status()
            print(f"总活跃上下文: {status['total_contexts']}")
            print(f"优先级资源池剩余: {status['priority_available']}")
            
            assert priority_context is not None
            
            # 清理优先级上下文
            if priority_context:
                await browser_manager.close_context(priority_context)
                
        finally:
            # 清理所有普通上下文
            for context in normal_contexts:
                if context:
                    await browser_manager.close_context(context)
    
    async def test_timeout_mechanism(self):
        """测试超时机制"""
        print("测试超时机制...")
        
        await browser_manager.start()
        
        # 占用所有资源
        contexts = []
        try:
            # 占用所有普通和优先级资源
            for i in range(10):  # 超过总资源数
                try:
                    context = await browser_manager.create_context(
                        operation_type=f"test_occupy_{i}",
                        priority=False,
                        timeout=2.0
                    )
                    contexts.append(context)
                except Exception as e:
                    print(f"预期的资源耗尽: {e}")
                    break
            
            print(f"已占用 {len(contexts)} 个上下文")
            
            # 现在尝试创建新上下文，应该超时
            start_time = time.time()
            timeout_occurred = False
            
            try:
                await browser_manager.create_context(
                    operation_type="test_timeout",
                    priority=False,
                    timeout=3.0
                )
            except Exception as e:
                timeout_time = time.time() - start_time
                print(f"预期的超时发生，耗时: {timeout_time:.2f}秒，错误: {e}")
                timeout_occurred = True
                assert "超时" in str(e)
                assert 2.5 <= timeout_time <= 4.0  # 允许一些误差
            
            assert timeout_occurred, "应该发生超时"
            
        finally:
            # 清理资源
            for context in contexts:
                if context:
                    await browser_manager.close_context(context)
    
    async def test_resource_monitoring(self):
        """测试资源监控功能"""
        print("测试资源监控功能...")
        
        await browser_manager.start()
        
        # 创建一些上下文
        contexts = []
        try:
            for i in range(3):
                context = await browser_manager.create_context(
                    operation_type=f"test_monitor_{i}",
                    priority=i == 0,  # 第一个使用高优先级
                    timeout=10.0
                )
                contexts.append(context)
                await asyncio.sleep(0.1)  # 稍微间隔一下
            
            # 获取资源状态
            status = browser_manager.get_resource_status()
            
            print("资源状态:")
            print(f"  总上下文数: {status['total_contexts']}")
            print(f"  普通资源池剩余: {status['normal_available']}")
            print(f"  优先级资源池剩余: {status['priority_available']}")
            
            assert status['total_contexts'] == 3
            assert len(status['active_contexts']) == 3
            
            # 检查上下文信息
            for ctx_info in status['active_contexts']:
                assert 'id' in ctx_info
                assert 'operation_type' in ctx_info
                assert 'priority' in ctx_info
                assert 'age_seconds' in ctx_info
                print(f"  上下文: {ctx_info['id']}, 类型: {ctx_info['operation_type']}, "
                      f"优先级: {ctx_info['priority']}, 年龄: {ctx_info['age_seconds']:.1f}s")
            
            # 测试日志记录
            browser_manager._log_active_contexts()
            
        finally:
            # 清理资源
            for context in contexts:
                if context:
                    await browser_manager.close_context(context)


if __name__ == "__main__":
    async def run_tests():
        test_instance = TestBrowserResourceManagement()
        
        print("开始浏览器资源管理测试...")
        
        try:
            await test_instance.test_concurrent_context_creation()
            print("✅ 并发上下文创建测试通过\n")
            
            await test_instance.test_priority_context_creation()
            print("✅ 优先级上下文创建测试通过\n")
            
            await test_instance.test_timeout_mechanism()
            print("✅ 超时机制测试通过\n")
            
            await test_instance.test_resource_monitoring()
            print("✅ 资源监控测试通过\n")
            
            print("🎉 所有测试通过！")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            raise
        finally:
            # 确保清理
            await browser_manager.stop()
    
    asyncio.run(run_tests())
