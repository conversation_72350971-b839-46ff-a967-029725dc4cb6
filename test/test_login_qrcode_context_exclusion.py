#!/usr/bin/env python3
"""
测试登录二维码上下文互斥逻辑
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.browser_manager import BrowserManager


async def test_login_qrcode_exclusion():
    """测试登录二维码上下文的互斥逻辑"""
    print("🧪 测试登录二维码上下文互斥逻辑")
    print("=" * 50)
    
    browser_manager = BrowserManager()
    
    try:
        # 启动浏览器管理器
        await browser_manager.start()
        print("✅ 浏览器管理器启动成功")
        
        # 测试场景1：同平台多个登录二维码上下文
        print("\n📋 测试场景1：同平台多个登录二维码上下文")
        
        # 创建第一个微信公众号登录二维码上下文
        print("创建第一个微信公众号登录二维码上下文...")
        context1 = await browser_manager.create_context(
            priority=True,
            timeout=10.0,
            operation_type="wechat_mp_login_qrcode"
        )
        print("✅ 第一个上下文创建成功")
        
        # 检查活跃上下文
        status = browser_manager.get_resource_status()
        print(f"📊 当前活跃上下文数量: {status['total_contexts']}")
        browser_manager._log_active_contexts()
        
        # 等待一下
        await asyncio.sleep(2)
        
        # 创建第二个微信公众号登录二维码上下文（应该清理第一个）
        print("\n创建第二个微信公众号登录二维码上下文...")
        context2 = await browser_manager.create_context(
            priority=True,
            timeout=10.0,
            operation_type="wechat_mp_login_qrcode"
        )
        print("✅ 第二个上下文创建成功")
        
        # 检查活跃上下文（应该只有一个）
        status = browser_manager.get_resource_status()
        print(f"📊 当前活跃上下文数量: {status['total_contexts']}")
        browser_manager._log_active_contexts()
        
        # 验证第一个上下文是否已被清理
        try:
            # 尝试使用第一个上下文（应该失败）
            page1 = await context1.new_page()
            print("❌ 第一个上下文仍然可用（不符合预期）")
            await page1.close()
        except Exception as e:
            print("✅ 第一个上下文已被清理（符合预期）")
        
        # 测试场景2：不同平台的登录二维码上下文应该共存
        print("\n📋 测试场景2：不同平台的登录二维码上下文")
        
        # 创建小红书登录二维码上下文
        print("创建小红书登录二维码上下文...")
        context3 = await browser_manager.create_context(
            priority=True,
            timeout=10.0,
            operation_type="xiaohongshu_login_qrcode"
        )
        print("✅ 小红书上下文创建成功")
        
        # 检查活跃上下文（应该有两个：微信公众号和小红书）
        status = browser_manager.get_resource_status()
        print(f"📊 当前活跃上下文数量: {status['total_contexts']}")
        browser_manager._log_active_contexts()
        
        # 验证两个上下文都可用
        try:
            page2 = await context2.new_page()
            page3 = await context3.new_page()
            print("✅ 不同平台的上下文可以共存")
            await page2.close()
            await page3.close()
        except Exception as e:
            print(f"❌ 上下文使用失败: {e}")
        
        # 测试场景3：创建第二个小红书登录二维码上下文
        print("\n📋 测试场景3：同平台替换测试")
        
        print("创建第二个小红书登录二维码上下文...")
        context4 = await browser_manager.create_context(
            priority=True,
            timeout=10.0,
            operation_type="xiaohongshu_login_qrcode"
        )
        print("✅ 第二个小红书上下文创建成功")
        
        # 检查活跃上下文（应该还是两个：微信公众号和新的小红书）
        status = browser_manager.get_resource_status()
        print(f"📊 当前活跃上下文数量: {status['total_contexts']}")
        browser_manager._log_active_contexts()
        
        # 验证旧的小红书上下文是否被清理
        try:
            page3_new = await context3.new_page()
            print("❌ 旧的小红书上下文仍然可用（不符合预期）")
            await page3_new.close()
        except Exception as e:
            print("✅ 旧的小红书上下文已被清理（符合预期）")
        
        # 测试场景4：清理过期上下文
        print("\n📋 测试场景4：清理过期上下文")
        
        # 等待一下让上下文变"老"
        await asyncio.sleep(3)
        
        # 清理过期上下文（设置很短的过期时间）
        cleaned_count = await browser_manager.cleanup_expired_contexts(max_age_seconds=1)
        print(f"✅ 清理了 {cleaned_count} 个过期上下文")
        
        # 检查清理后的状态
        status = browser_manager.get_resource_status()
        print(f"📊 清理后活跃上下文数量: {status['total_contexts']}")
        browser_manager._log_active_contexts()
        
        print("\n🎯 测试完成！")
        print("=" * 50)
        print("✅ 验证要点：")
        print("1. 同平台登录二维码上下文互斥（新的替换旧的）")
        print("2. 不同平台登录二维码上下文可以共存")
        print("3. 过期上下文可以被正确清理")
        print("4. 资源信号量正确释放")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        try:
            await browser_manager.stop()
            print("✅ 浏览器管理器已停止")
        except Exception as e:
            print(f"⚠️  停止浏览器管理器时出错: {e}")


async def test_resource_status():
    """测试资源状态监控"""
    print("\n🔍 测试资源状态监控")
    print("=" * 30)
    
    browser_manager = BrowserManager()
    
    try:
        await browser_manager.start()
        
        # 初始状态
        status = browser_manager.get_resource_status()
        print(f"📊 初始状态: {status}")
        
        # 创建一些上下文
        contexts = []
        for i in range(3):
            context = await browser_manager.create_context(
                priority=i < 2,  # 前两个是高优先级
                operation_type=f"test_context_{i}"
            )
            contexts.append(context)
        
        # 检查状态
        status = browser_manager.get_resource_status()
        print(f"📊 创建3个上下文后: {status}")
        browser_manager._log_active_contexts()
        
        # 关闭上下文
        for context in contexts:
            await browser_manager.close_context(context)
        
        # 最终状态
        status = browser_manager.get_resource_status()
        print(f"📊 清理后状态: {status}")
        
    finally:
        await browser_manager.stop()


if __name__ == "__main__":
    asyncio.run(test_login_qrcode_exclusion())
    asyncio.run(test_resource_status())
