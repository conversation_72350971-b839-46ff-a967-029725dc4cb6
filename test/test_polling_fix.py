#!/usr/bin/env python3
"""
测试数据更新轮询修复效果
"""

import asyncio
import aiohttp
import json
from datetime import datetime
import time

class PollingTest:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def login(self, username="admin", password="admin123"):
        """登录获取token"""
        try:
            data = {
                "username": username,
                "password": password
            }
            async with self.session.post(f"{self.base_url}/api/auth/login", data=data) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    token = result.get("access_token")
                    self.session.headers.update({"Authorization": f"Bearer {token}"})
                    print("✅ 登录成功")
                    return True
                else:
                    print(f"❌ 登录失败: {resp.status}")
                    return False
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    async def start_update_task(self):
        """启动数据更新任务"""
        try:
            data = {
                "start_date": "2024-01-01",
                "end_date": "2024-01-02"
            }
            async with self.session.post(f"{self.base_url}/api/data-update/start", json=data) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    if result.get("success"):
                        task_id = result.get("task_id")
                        print(f"✅ 任务启动成功，ID: {task_id}")
                        return task_id
                    else:
                        print(f"❌ 任务启动失败: {result}")
                        return None
                else:
                    text = await resp.text()
                    print(f"❌ 任务启动失败: {resp.status} - {text}")
                    return None
        except Exception as e:
            print(f"❌ 启动任务异常: {e}")
            return None
    
    async def get_task_status(self, task_id):
        """获取任务状态"""
        try:
            async with self.session.get(f"{self.base_url}/api/data-update/status/{task_id}") as resp:
                if resp.status == 200:
                    result = await resp.json()
                    return result
                elif resp.status == 404:
                    print(f"⚠️  任务 {task_id} 不存在")
                    return None
                else:
                    text = await resp.text()
                    print(f"❌ 获取状态失败: {resp.status} - {text}")
                    return None
        except Exception as e:
            print(f"❌ 获取状态异常: {e}")
            return None
    
    async def stop_task(self, task_id):
        """停止任务"""
        try:
            async with self.session.post(f"{self.base_url}/api/data-update/tasks/{task_id}/stop") as resp:
                if resp.status == 200:
                    result = await resp.json()
                    print(f"✅ 任务停止成功: {result}")
                    return True
                else:
                    text = await resp.text()
                    print(f"❌ 停止任务失败: {resp.status} - {text}")
                    return False
        except Exception as e:
            print(f"❌ 停止任务异常: {e}")
            return False
    
    async def delete_task(self, task_id):
        """删除任务"""
        try:
            async with self.session.delete(f"{self.base_url}/api/data-update/tasks/{task_id}") as resp:
                if resp.status == 200:
                    result = await resp.json()
                    print(f"✅ 任务删除成功: {result}")
                    return True
                else:
                    text = await resp.text()
                    print(f"❌ 删除任务失败: {resp.status} - {text}")
                    return False
        except Exception as e:
            print(f"❌ 删除任务异常: {e}")
            return False
    
    async def simulate_polling(self, task_id, max_polls=10):
        """模拟前端轮询行为"""
        print(f"\n🔄 开始模拟轮询任务 {task_id}")
        
        for i in range(max_polls):
            print(f"📊 轮询 #{i+1}")
            status = await self.get_task_status(task_id)
            
            if status is None:
                print("❌ 任务状态获取失败或任务不存在，应该停止轮询")
                break
            
            if status.get("success"):
                task_status = status.get("status")
                print(f"📋 任务状态: {task_status}")
                
                # 检查是否应该停止轮询
                finished_statuses = ['completed', 'failed', 'cancelled', 'stopped']
                if task_status in finished_statuses:
                    print(f"✅ 任务已结束 ({task_status})，轮询应该停止")
                    break
            
            await asyncio.sleep(2)  # 模拟2秒间隔
        
        print("🔄 轮询结束")

async def test_polling_scenarios():
    """测试各种轮询场景"""
    print("🧪 开始测试数据更新轮询修复效果")
    print("=" * 50)
    
    async with PollingTest() as test:
        # 1. 登录
        if not await test.login():
            print("❌ 无法登录，测试终止")
            return
        
        # 2. 测试场景1：正常任务完成
        print("\n📋 测试场景1：正常任务完成")
        task_id = await test.start_update_task()
        if task_id:
            # 模拟轮询几次
            await asyncio.sleep(5)  # 等待任务开始
            await test.simulate_polling(task_id, max_polls=5)
            
            # 停止任务
            await test.stop_task(task_id)
            
            # 再次轮询，应该检测到任务已停止
            await test.simulate_polling(task_id, max_polls=3)
        
        # 3. 测试场景2：任务被删除
        print("\n📋 测试场景2：任务被删除")
        task_id = await test.start_update_task()
        if task_id:
            await asyncio.sleep(3)  # 等待任务开始
            
            # 删除任务
            await test.delete_task(task_id)
            
            # 继续轮询，应该检测到404并停止
            await test.simulate_polling(task_id, max_polls=3)
        
        print("\n🎯 测试完成！")
        print("=" * 50)
        print("✅ 修复要点：")
        print("1. 轮询在任务完成/失败/取消/停止时正确停止")
        print("2. 轮询在任务不存在(404)时正确停止")
        print("3. 轮询在网络错误时正确停止")
        print("4. 组件卸载时清理轮询")
        print("5. 页面隐藏时暂停轮询，显示时恢复")

if __name__ == "__main__":
    asyncio.run(test_polling_scenarios())
