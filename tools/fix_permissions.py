#!/usr/bin/env python3
"""
Docker权限修复工具
解决容器中文件权限问题
"""

import os
import sys
import subprocess
import stat
from pathlib import Path

def print_banner():
    """打印横幅"""
    print("=" * 70)
    print("  🔧 Docker权限修复工具")
    print("=" * 70)
    print()

def check_docker_status():
    """检查Docker状态"""
    print("🐳 检查Docker状态...")
    
    try:
        result = subprocess.run(['docker', 'ps'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Docker运行正常")
            return True
        else:
            print("❌ Docker未运行或无权限访问")
            return False
    except FileNotFoundError:
        print("❌ Docker未安装")
        return False

def get_current_user_info():
    """获取当前用户信息"""
    uid = os.getuid()
    gid = os.getgid()
    username = os.getenv('USER', 'unknown')
    
    print(f"👤 当前用户: {username} (UID: {uid}, GID: {gid})")
    return uid, gid

def check_directory_permissions():
    """检查目录权限"""
    print("\n📁 检查目录权限...")
    
    directories = ['user_data', 'data', 'temp_downloads']
    issues = []
    
    for dir_name in directories:
        dir_path = Path(dir_name)
        
        if not dir_path.exists():
            print(f"⚠️  目录不存在: {dir_name}")
            issues.append(f"create_{dir_name}")
        else:
            # 检查权限
            stat_info = dir_path.stat()
            permissions = stat.filemode(stat_info.st_mode)
            owner_uid = stat_info.st_uid
            owner_gid = stat_info.st_gid
            
            print(f"📋 {dir_name}: {permissions} (UID: {owner_uid}, GID: {owner_gid})")
            
            # 检查是否可写
            if not os.access(dir_path, os.W_OK):
                print(f"❌ {dir_name} 无写权限")
                issues.append(f"fix_{dir_name}")
            else:
                print(f"✅ {dir_name} 权限正常")
    
    return issues

def fix_permissions(issues, uid, gid):
    """修复权限问题"""
    if not issues:
        print("\n🎉 所有目录权限正常，无需修复")
        return True
    
    print(f"\n🔧 修复权限问题...")
    print(f"将设置所有者为: UID {uid}, GID {gid}")
    
    try:
        # 创建缺失的目录
        for issue in issues:
            if issue.startswith('create_'):
                dir_name = issue.replace('create_', '')
                print(f"📁 创建目录: {dir_name}")
                os.makedirs(dir_name, exist_ok=True)
        
        # 修复权限
        directories = ['user_data', 'data', 'temp_downloads']
        for dir_name in directories:
            if os.path.exists(dir_name):
                print(f"🔧 修复 {dir_name} 权限...")
                
                # 递归设置所有者
                for root, dirs, files in os.walk(dir_name):
                    os.chown(root, uid, gid)
                    os.chmod(root, 0o755)
                    
                    for file in files:
                        file_path = os.path.join(root, file)
                        os.chown(file_path, uid, gid)
                        os.chmod(file_path, 0o644)
                
                print(f"✅ {dir_name} 权限修复完成")
        
        return True
        
    except PermissionError as e:
        print(f"❌ 权限不足，无法修复: {e}")
        print("💡 请使用sudo运行此脚本")
        return False
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        return False

def restart_docker_containers():
    """重启Docker容器"""
    print("\n🔄 重启Docker容器...")
    
    try:
        # 查找相关容器
        result = subprocess.run([
            'docker', 'ps', '-a', '--filter', 'name=social_media', '--format', '{{.Names}}'
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print("❌ 无法查询Docker容器")
            return False
        
        containers = result.stdout.strip().split('\n')
        containers = [c for c in containers if c.strip()]
        
        if not containers:
            print("ℹ️  未找到相关容器")
            return True
        
        print(f"找到容器: {', '.join(containers)}")
        
        # 重启容器
        for container in containers:
            print(f"🔄 重启容器: {container}")
            restart_result = subprocess.run(['docker', 'restart', container], capture_output=True, text=True)
            
            if restart_result.returncode == 0:
                print(f"✅ {container} 重启成功")
            else:
                print(f"❌ {container} 重启失败: {restart_result.stderr}")
        
        return True
        
    except Exception as e:
        print(f"❌ 重启容器时出错: {e}")
        return False

def show_docker_compose_fix():
    """显示docker-compose修复方法"""
    print("\n📋 Docker Compose修复方法:")
    print("-" * 50)
    print("如果问题持续存在，可以尝试以下方法：")
    print()
    print("1️⃣ 停止所有容器:")
    print("   docker-compose down")
    print()
    print("2️⃣ 重新构建镜像:")
    print("   docker-compose build --no-cache")
    print()
    print("3️⃣ 启动服务:")
    print("   docker-compose up -d")
    print()
    print("4️⃣ 查看日志:")
    print("   docker-compose logs -f backend")
    print()

def main():
    """主函数"""
    print_banner()
    
    # 检查Docker状态
    if not check_docker_status():
        print("请确保Docker正常运行后重试")
        return 1
    
    # 获取用户信息
    uid, gid = get_current_user_info()
    
    # 检查目录权限
    issues = check_directory_permissions()
    
    # 修复权限
    if not fix_permissions(issues, uid, gid):
        print("\n❌ 权限修复失败")
        show_docker_compose_fix()
        return 1
    
    # 询问是否重启容器
    print("\n🤔 是否重启Docker容器以应用更改？")
    restart = input("重启容器? (y/N): ").strip().lower()
    
    if restart in ['y', 'yes']:
        if restart_docker_containers():
            print("\n🎉 权限修复完成，容器已重启")
        else:
            print("\n⚠️  权限已修复，但容器重启失败，请手动重启")
            show_docker_compose_fix()
    else:
        print("\n✅ 权限修复完成")
        print("💡 请手动重启容器以应用更改: docker-compose restart")
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 执行过程中发生错误: {e}")
        sys.exit(1)
