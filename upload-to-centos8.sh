#!/bin/bash

# 上传项目到CentOS 8服务器脚本

set -e

# 默认配置
SERVER_USER="root"
SERVER_HOST=""
SERVER_PATH="/home/<USER>/social-media-management"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --host HOST     服务器IP地址"
    echo "  -u, --user USER     服务器用户名 (默认: root)"
    echo "  -p, --path PATH     服务器部署路径 (默认: /home/<USER>/social-media-management)"
    echo "  --help              显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -h *************"
    echo "  $0 -h ************* -u root -p /opt/social-media"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--host)
                SERVER_HOST="$2"
                shift 2
                ;;
            -u|--user)
                SERVER_USER="$2"
                shift 2
                ;;
            -p|--path)
                SERVER_PATH="$2"
                shift 2
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # 检查必需参数
    if [ -z "$SERVER_HOST" ]; then
        log_error "请指定服务器IP地址"
        show_usage
        exit 1
    fi
}

# 检查本地环境
check_local_env() {
    log_info "检查本地环境..."
    
    # 检查rsync
    if ! command -v rsync &> /dev/null; then
        log_error "rsync未安装，请先安装rsync"
        exit 1
    fi
    
    # 检查ssh
    if ! command -v ssh &> /dev/null; then
        log_error "ssh未安装，请先安装ssh"
        exit 1
    fi
    
    # 检查项目文件
    if [ ! -f "requirements.txt" ]; then
        log_error "未找到requirements.txt文件，请在项目根目录运行此脚本"
        exit 1
    fi
    
    if [ ! -f "main.py" ]; then
        log_error "未找到main.py文件，请在项目根目录运行此脚本"
        exit 1
    fi
    
    log_success "本地环境检查通过"
}

# 测试服务器连接
test_connection() {
    log_info "测试服务器连接..."
    
    if ssh -o ConnectTimeout=10 -o BatchMode=yes $SERVER_USER@$SERVER_HOST exit 2>/dev/null; then
        log_success "服务器连接正常"
    else
        log_error "无法连接到服务器，请检查："
        log_error "1. 服务器IP地址是否正确"
        log_error "2. SSH密钥是否配置正确"
        log_error "3. 服务器是否允许SSH连接"
        exit 1
    fi
}

# 创建排除文件列表
create_exclude_list() {
    log_info "创建文件排除列表..."
    
    cat > .rsync-exclude << EOF
.git/
.gitignore
node_modules/
venv/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.DS_Store
.vscode/
.idea/
*.swp
*.swo
*~
.env.local
.env.development
.env.test
social_media.db
social_media_manager.db
social_media_*.db
user_data/
temp_downloads/
logs/
frontend/build/
frontend/node_modules/
*.backup
*.bak
.rsync-exclude
EOF
    
    log_success "排除列表创建完成"
}

# 准备配置文件
prepare_configs() {
    log_info "准备配置文件..."
    
    # 创建生产环境配置
    if [ ! -f ".env.production" ]; then
        cp .env.example .env.production
        
        # 生成随机密钥
        SECRET_KEY=$(openssl rand -base64 32 2>/dev/null || date | md5sum | cut -d' ' -f1)
        sed -i "s/your_secret_key_here_generate_a_new_one/$SECRET_KEY/" .env.production
        sed -i "s|DATABASE_URL=sqlite:///./social_media.db|DATABASE_URL=sqlite://$SERVER_PATH/data/social_media.db|" .env.production
        sed -i "s/HOT_RELOAD=false/HOT_RELOAD=false/" .env.production
        sed -i "s/ENVIRONMENT=production/ENVIRONMENT=production/" .env.production
        
        log_info "生成了新的.env.production文件"
    fi
    
    log_success "配置文件准备完成"
}

# 上传文件
upload_files() {
    log_info "上传项目文件到服务器..."
    
    # 在服务器上创建目录
    ssh $SERVER_USER@$SERVER_HOST "mkdir -p $SERVER_PATH"
    
    # 使用rsync上传文件
    log_info "正在同步文件..."
    rsync -avz --progress \
        --exclude-from=.rsync-exclude \
        ./ $SERVER_USER@$SERVER_HOST:$SERVER_PATH/
    
    # 上传配置文件
    log_info "上传配置文件..."
    scp .env.production $SERVER_USER@$SERVER_HOST:$SERVER_PATH/.env
    
    # 上传部署脚本
    if [ -f "centos8-one-click-deploy.sh" ]; then
        scp centos8-one-click-deploy.sh $SERVER_USER@$SERVER_HOST:$SERVER_PATH/
    fi
    
    if [ -f "native-deploy.sh" ]; then
        scp native-deploy.sh $SERVER_USER@$SERVER_HOST:$SERVER_PATH/
    fi
    
    log_success "文件上传完成"
}

# 设置服务器权限
setup_permissions() {
    log_info "设置服务器权限..."
    
    ssh $SERVER_USER@$SERVER_HOST << EOF
        cd $SERVER_PATH
        
        # 设置脚本执行权限
        chmod +x *.sh 2>/dev/null || true
        
        # 创建必要目录
        mkdir -p user_data data temp_downloads logs configs
        
        # 如果web用户存在，设置权限
        if id "web" &>/dev/null; then
            chown -R web:web $SERVER_PATH
            chmod -R 755 user_data data temp_downloads logs
        fi
EOF
    
    log_success "权限设置完成"
}

# 清理临时文件
cleanup() {
    log_info "清理临时文件..."
    
    rm -f .rsync-exclude
    
    log_success "清理完成"
}

# 显示部署说明
show_deployment_instructions() {
    echo ""
    echo "🎉 文件上传完成！"
    echo "=================================="
    echo "📋 接下来请在服务器上执行以下命令："
    echo ""
    echo "1. 登录服务器:"
    echo "   ssh $SERVER_USER@$SERVER_HOST"
    echo ""
    echo "2. 进入项目目录:"
    echo "   cd $SERVER_PATH"
    echo ""
    echo "3. 运行一键部署脚本:"
    echo "   ./centos8-one-click-deploy.sh"
    echo ""
    echo "   或者运行通用部署脚本:"
    echo "   ./native-deploy.sh"
    echo ""
    echo "4. 手动部署（如果脚本失败）:"
    echo "   参考 centos8-deploy-guide.md 文档"
    echo ""
    echo "📊 部署完成后的访问地址:"
    echo "   http://$SERVER_HOST"
    echo ""
    echo "🔧 常用管理命令:"
    echo "   查看服务状态: supervisorctl status"
    echo "   查看日志: tail -f $SERVER_PATH/logs/backend.log"
    echo "   重启服务: supervisorctl restart social-media-backend"
}

# 主函数
main() {
    echo "🚀 上传项目到CentOS 8服务器"
    echo "============================="
    
    parse_args "$@"
    check_local_env
    test_connection
    create_exclude_list
    prepare_configs
    upload_files
    setup_permissions
    cleanup
    show_deployment_instructions
}

# 执行主函数
main "$@"
