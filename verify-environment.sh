#!/bin/bash

# 环境验证脚本
# 验证Python 3.11.13和Node.js 24是否正确安装

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 验证Python版本
verify_python() {
    log_info "验证Python环境..."
    
    # 检查python3.11命令
    if command -v python3.11 &> /dev/null; then
        PYTHON_VERSION=$(python3.11 --version | cut -d' ' -f2)
        if [[ "$PYTHON_VERSION" == "3.11.13" ]]; then
            log_success "Python 3.11.13 已正确安装"
            echo "  路径: $(which python3.11)"
            echo "  版本: $PYTHON_VERSION"
        else
            log_warning "Python版本不匹配: 期望3.11.13, 实际$PYTHON_VERSION"
        fi
    else
        log_error "未找到python3.11命令"
        return 1
    fi
    
    # 检查python3符号链接
    if command -v python3 &> /dev/null; then
        PYTHON3_VERSION=$(python3 --version | cut -d' ' -f2)
        echo "  python3版本: $PYTHON3_VERSION"
        echo "  python3路径: $(which python3)"
    else
        log_warning "未找到python3符号链接"
    fi
    
    # 检查pip3.11
    if command -v pip3.11 &> /dev/null; then
        PIP_VERSION=$(pip3.11 --version | cut -d' ' -f2)
        log_success "pip3.11 已安装: $PIP_VERSION"
        echo "  路径: $(which pip3.11)"
    else
        log_error "未找到pip3.11命令"
        return 1
    fi
    
    # 测试Python功能
    log_info "测试Python功能..."
    
    # 测试SSL支持
    if python3.11 -c "import ssl; print('SSL支持: OK')" 2>/dev/null; then
        log_success "SSL支持正常"
    else
        log_error "SSL支持异常"
    fi
    
    # 测试SQLite支持
    if python3.11 -c "import sqlite3; print('SQLite支持: OK')" 2>/dev/null; then
        log_success "SQLite支持正常"
    else
        log_error "SQLite支持异常"
    fi
    
    # 测试虚拟环境创建
    log_info "测试虚拟环境创建..."
    TEMP_VENV="/tmp/test_venv_$$"
    if python3.11 -m venv "$TEMP_VENV" 2>/dev/null; then
        log_success "虚拟环境创建正常"
        rm -rf "$TEMP_VENV"
    else
        log_error "虚拟环境创建失败"
    fi
}

# 验证Node.js版本
verify_nodejs() {
    log_info "验证Node.js环境..."
    
    # 检查node命令
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version | cut -d'v' -f2)
        NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1)
        
        if [[ "$NODE_MAJOR" == "24" ]]; then
            log_success "Node.js 24.x 已正确安装"
            echo "  版本: v$NODE_VERSION"
            echo "  路径: $(which node)"
        else
            log_warning "Node.js版本不匹配: 期望24.x, 实际v$NODE_VERSION"
        fi
    else
        log_error "未找到node命令"
        return 1
    fi
    
    # 检查npm命令
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        log_success "npm已安装: $NPM_VERSION"
        echo "  路径: $(which npm)"
        
        # 检查npm配置
        NPM_REGISTRY=$(npm config get registry)
        echo "  镜像源: $NPM_REGISTRY"
        
        if [[ "$NPM_REGISTRY" == *"npmmirror.com"* ]]; then
            log_success "npm已配置国内镜像源"
        else
            log_warning "npm未配置国内镜像源"
        fi
    else
        log_error "未找到npm命令"
        return 1
    fi
}

# 验证系统依赖
verify_system_deps() {
    log_info "验证系统依赖..."
    
    # 检查编译工具
    DEPS=("gcc" "g++" "make" "git" "curl" "wget")
    for dep in "${DEPS[@]}"; do
        if command -v "$dep" &> /dev/null; then
            log_success "$dep 已安装"
        else
            log_error "$dep 未安装"
        fi
    done
    
    # 检查开发库
    DEV_LIBS=("openssl-devel" "libffi-devel" "sqlite-devel")
    for lib in "${DEV_LIBS[@]}"; do
        if rpm -q "$lib" &> /dev/null; then
            log_success "$lib 已安装"
        else
            log_warning "$lib 可能未安装"
        fi
    done
}

# 验证项目依赖
verify_project_deps() {
    log_info "验证项目依赖..."
    
    # 检查项目文件
    if [ -f "requirements.txt" ]; then
        log_success "找到requirements.txt"
        echo "  Python依赖数量: $(wc -l < requirements.txt)"
    else
        log_warning "未找到requirements.txt"
    fi
    
    if [ -f "main.py" ]; then
        log_success "找到main.py"
    else
        log_warning "未找到main.py"
    fi
    
    if [ -d "frontend" ]; then
        log_success "找到frontend目录"
        
        if [ -f "frontend/package.json" ]; then
            log_success "找到frontend/package.json"
        else
            log_warning "未找到frontend/package.json"
        fi
    else
        log_warning "未找到frontend目录"
    fi
    
    # 检查虚拟环境
    if [ -d "venv" ]; then
        log_success "找到Python虚拟环境"
        
        if [ -f "venv/bin/activate" ]; then
            log_success "虚拟环境可用"
        else
            log_warning "虚拟环境可能损坏"
        fi
    else
        log_info "未找到Python虚拟环境（这是正常的，如果还未创建）"
    fi
}

# 验证服务配置
verify_services() {
    log_info "验证服务配置..."
    
    # 检查Nginx
    if command -v nginx &> /dev/null; then
        log_success "Nginx已安装"
        echo "  版本: $(nginx -v 2>&1 | cut -d' ' -f3)"
        
        # 测试配置
        if nginx -t &> /dev/null; then
            log_success "Nginx配置正确"
        else
            log_warning "Nginx配置可能有问题"
        fi
    else
        log_warning "Nginx未安装"
    fi
    
    # 检查Supervisor
    if command -v supervisorctl &> /dev/null; then
        log_success "Supervisor已安装"
        
        if systemctl is-active supervisord &> /dev/null; then
            log_success "Supervisor服务运行中"
        else
            log_info "Supervisor服务未运行"
        fi
    else
        log_warning "Supervisor未安装"
    fi
    
    # 检查防火墙
    if command -v firewall-cmd &> /dev/null; then
        log_success "防火墙已安装"
        
        if systemctl is-active firewalld &> /dev/null; then
            log_success "防火墙服务运行中"
            
            # 检查HTTP端口
            if firewall-cmd --list-services | grep -q http; then
                log_success "HTTP端口已开放"
            else
                log_warning "HTTP端口未开放"
            fi
        else
            log_info "防火墙服务未运行"
        fi
    else
        log_warning "防火墙未安装"
    fi
}

# 显示总结
show_summary() {
    echo ""
    echo "🔍 环境验证完成"
    echo "=================================="
    echo ""
    echo "📋 验证项目:"
    echo "  ✓ Python 3.11.13"
    echo "  ✓ Node.js 24.x"
    echo "  ✓ 系统依赖"
    echo "  ✓ 项目文件"
    echo "  ✓ 服务配置"
    echo ""
    echo "💡 如果发现问题，请参考部署文档进行修复"
    echo "📚 文档: centos8-deploy-guide.md"
}

# 主函数
main() {
    echo "🔍 CentOS 8 环境验证脚本"
    echo "========================="
    echo ""
    
    verify_python
    echo ""
    verify_nodejs
    echo ""
    verify_system_deps
    echo ""
    verify_project_deps
    echo ""
    verify_services
    
    show_summary
}

# 执行主函数
main "$@"
